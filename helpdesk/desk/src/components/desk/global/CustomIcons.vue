<template>
  <div>
    <svg
      v-if="this.name == 'external-link'"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M16.2504 13.0556V14.5001C16.2504 16.1569 14.9072 17.5001 13.2504 17.5001H5.917C4.26014 17.5001 2.91699 16.1569 2.91699 14.5001V7.16674C2.91699 5.50991 4.25988 4.16677 5.91671 4.16675C6.39175 4.16675 6.88395 4.16675 7.36149 4.16675"
        stroke="#505A62"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M11.25 2.5H17.0833V7.5"
        stroke="#505A62"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M11.25 8.33325L16.25 3.33325"
        stroke="#505A62"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
    <svg
      v-if="this.name == 'log-out'"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M3 10L4.89662 10.0054L7.79323 10.0107L9.24154 10.0134L10.9657 10.0147"
        stroke="#E24C4C"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M8.5 7L11.5 10L8.5 13"
        stroke="#E24C4C"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M3.12402 13C4.28136 15.6489 6.92448 17.5 9.99996 17.5C14.1421 17.5 17.5 14.1421 17.5 10C17.5 5.85786 14.1421 2.5 9.99996 2.5C6.92448 2.5 4.28136 4.35114 3.12402 7"
        stroke="#E24C4C"
        stroke-linecap="round"
      />
    </svg>
    <svg
      v-if="this.name == 'priority-urgent'"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 9 9"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        width="2"
        height="6"
        rx="1"
        transform="matrix(-1 0 0 1 5.5 0)"
        fill="#E24C4C"
      />
      <rect
        width="2"
        height="2"
        rx="1"
        transform="matrix(-1 0 0 1 5.5 7)"
        fill="#E24C4C"
      />
    </svg>
    <svg
      v-if="this.name == 'priority-medium'"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 9 9"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        width="2"
        height="3"
        rx="1"
        transform="matrix(-1 0 0 1 2.5 6)"
        fill="#A6B1B9"
      />
      <rect
        width="2"
        height="6"
        rx="1"
        transform="matrix(-1 0 0 1 5.5 3)"
        fill="#A6B1B9"
      />
      <rect
        width="2"
        height="9"
        rx="1"
        transform="matrix(-1 0 0 1 8.5 0)"
        fill="#EBEEF0"
      />
    </svg>
    <svg
      v-if="this.name == 'priority-low'"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 9 9"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        width="2"
        height="3"
        rx="1"
        transform="matrix(-1 0 0 1 2.5 6)"
        fill="#A6B1B9"
      />
      <rect
        width="2"
        height="6"
        rx="1"
        transform="matrix(-1 0 0 1 5.5 3)"
        fill="#EBEEF0"
      />
      <rect
        width="2"
        height="9"
        rx="1"
        transform="matrix(-1 0 0 1 8.5 0)"
        fill="#EBEEF0"
      />
    </svg>
    <svg
      v-if="this.name == 'priority-high'"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 9 9"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        width="2"
        height="3"
        rx="1"
        transform="matrix(-1 0 0 1 2.5 6)"
        fill="#A6B1B9"
      />
      <rect
        width="2"
        height="6"
        rx="1"
        transform="matrix(-1 0 0 1 5.5 3)"
        fill="#A6B1B9"
      />
      <rect
        width="2"
        height="9"
        rx="1"
        transform="matrix(-1 0 0 1 8.5 0)"
        fill="#A6B1B9"
      />
    </svg>
    <svg
      v-if="this.name == 'user-plus'"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 13 13"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M7.25 8.375H5C4.00544 8.375 3.05161 8.77009 2.34835 9.47335C1.64509 10.1766 1.25 11.1304 1.25 12.125H8"
        stroke="#74808B"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M6.125 6.125C7.57475 6.125 8.75 4.94975 8.75 3.5C8.75 2.05025 7.57475 0.875 6.125 0.875C4.67525 0.875 3.5 2.05025 3.5 3.5C3.5 4.94975 4.67525 6.125 6.125 6.125Z"
        stroke="#74808B"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M10.25 8.375V11.375"
        stroke="#74808B"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M8.75 9.875H11.75"
        stroke="#74808B"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
    <svg
      v-if="this.name == 'corner-up-left'"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 11 8"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10.9999 7.51503C10.9999 7.68835 10.9052 7.84845 10.7511 7.93505C10.5971 8.02165 10.4075 8.02165 10.2534 7.93505C10.0994 7.84845 10.0045 7.68834 10.0045 7.51503C10.003 6.35143 9.52785 5.23588 8.68346 4.41315C7.83907 3.59041 6.69417 3.12753 5.49998 3.12614H1.86175L3.44892 4.42778C3.55079 4.51038 3.61477 4.62913 3.62655 4.75773C3.63822 4.88644 3.59689 5.01429 3.51146 5.11312C3.42614 5.21184 3.30394 5.27344 3.17187 5.28405C3.03978 5.29477 2.90881 5.25363 2.80793 5.16984L0.177079 3.01171C0.0647672 2.91948 0 2.78373 0 2.64072C0 2.49761 0.0647644 2.36188 0.177079 2.26962L2.80782 0.111938L2.80793 0.112046C2.94402 0.00173586 3.13009 -0.0297653 3.29651 0.0293403C3.46282 0.088446 3.58446 0.229174 3.6158 0.399028C3.64724 0.568768 3.5837 0.742084 3.44894 0.854015L1.86235 2.15618H5.5C6.95808 2.1578 8.35587 2.72299 9.38707 3.72758C10.4181 4.73216 10.9982 6.0943 11 7.51507L10.9999 7.51503Z"
        fill="#74808B"
      />
    </svg>
    <svg
      v-if="this.name == 'check'"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 13 11"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M1 7.66667L4 10.5L12 1"
        stroke-miterlimit="10"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
    <svg
      v-if="this.name == 'circle-check'"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 18 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9 16.5C13.4183 16.5 17 12.9183 17 8.5C17 4.08172 13.4183 0.5 9 0.5C4.58172 0.5 1 4.08172 1 8.5C1 12.9183 4.58172 16.5 9 16.5Z"
        fill="#48BB74"
      />
      <path
        d="M5.6665 9.11357L7.6665 11.1136L12.3332 6.4469M17 8.5C17 12.9183 13.4183 16.5 9 16.5C4.58172 16.5 1 12.9183 1 8.5C1 4.08172 4.58172 0.5 9 0.5C13.4183 0.5 17 4.08172 17 8.5Z"
        stroke="white"
        stroke-miterlimit="10"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
    <svg
      v-if="this.name == 'circle-fail'"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M8.00016 14.6666C11.6821 14.6666 14.6668 11.6819 14.6668 7.99998C14.6668 4.31808 11.6821 1.33331 8.00016 1.33331C4.31826 1.33331 1.3335 4.31808 1.3335 7.99998C1.3335 11.6819 4.31826 14.6666 8.00016 14.6666ZM5.30317 5.30298C5.59606 5.01009 6.07094 5.01009 6.36383 5.30298L8.00092 6.94008L9.63799 5.30302C9.93088 5.01012 10.4058 5.01012 10.6986 5.30302C10.9915 5.59591 10.9915 6.07078 10.6986 6.36368L9.06159 8.00074L10.6986 9.6378C10.9915 9.93069 10.9915 10.4056 10.6986 10.6985C10.4058 10.9914 9.93088 10.9914 9.63799 10.6985L8.00093 9.0614L6.36383 10.6985C6.07094 10.9914 5.59606 10.9914 5.30317 10.6985C5.01028 10.4056 5.01027 9.93073 5.30317 9.63784L6.94026 8.00074L5.30317 6.36364C5.01028 6.07075 5.01028 5.59588 5.30317 5.30298Z"
        fill="#F56B6B"
      />
    </svg>
    <svg
      v-if="this.name == 'sla-pass'"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M14.6668 8.00001C14.6668 11.6819 11.6821 14.6667 8.00016 14.6667C4.31826 14.6667 1.3335 11.6819 1.3335 8.00001C1.3335 4.31811 4.31826 1.33334 8.00016 1.33334C11.6821 1.33334 14.6668 4.31811 14.6668 8.00001ZM11.4848 5.58164C11.0943 5.19112 10.4611 5.19112 10.0706 5.58164L9.51506 6.1372L8.40395 7.24831L6.88883 8.76342L6.48483 8.35942L5.92928 7.80386C5.53875 7.41334 4.90559 7.41334 4.51506 7.80386C4.12454 8.19439 4.12454 8.82755 4.51506 9.21808L5.07062 9.77363L6.18173 10.8847C6.36926 11.0723 6.62362 11.1776 6.88883 11.1776C7.15405 11.1776 7.4084 11.0723 7.59594 10.8847L9.81816 8.66252L10.9293 7.55141L11.4848 6.99585C11.8754 6.60533 11.8754 5.97217 11.4848 5.58164Z"
        fill="#2490EF"
      />
    </svg>
    <svg
      v-if="this.name == 'sla-fail'"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M8.00016 14.6666C11.6821 14.6666 14.6668 11.6819 14.6668 7.99998C14.6668 4.31808 11.6821 1.33331 8.00016 1.33331C4.31826 1.33331 1.3335 4.31808 1.3335 7.99998C1.3335 11.6819 4.31826 14.6666 8.00016 14.6666ZM5.30317 5.30298C5.59606 5.01009 6.07094 5.01009 6.36383 5.30298L8.00092 6.94008L9.63799 5.30302C9.93088 5.01012 10.4058 5.01012 10.6986 5.30302C10.9915 5.59591 10.9915 6.07078 10.6986 6.36368L9.06159 8.00074L10.6986 9.6378C10.9915 9.93069 10.9915 10.4056 10.6986 10.6985C10.4058 10.9914 9.93088 10.9914 9.63799 10.6985L8.00093 9.0614L6.36383 10.6985C6.07094 10.9914 5.59606 10.9914 5.30317 10.6985C5.01028 10.4056 5.01027 9.93073 5.30317 9.63784L6.94026 8.00074L5.30317 6.36364C5.01028 6.07075 5.01028 5.59588 5.30317 5.30298Z"
        fill="#F56B6B"
      />
    </svg>
    <svg
      v-if="this.name == 'disc'"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="16" height="16" rx="8" fill="#F9FAFA" />
      <rect
        x="1.27734"
        y="1.27759"
        width="13.4444"
        height="13.4444"
        rx="6.72222"
        fill="white"
        stroke="#DCE0E3"
      />
      <circle cx="8" cy="8" r="1.5" fill="#74808B" stroke="#74808B" />
    </svg>
    <svg
      v-if="this.name == 'lock'"
      class="stroke-0"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M7.8075 1.83351H7.75569C6.91253 1.8258 6.10072 2.15293 5.49845 2.74315C4.89557 3.33398 4.55202 4.14006 4.54337 4.98414L4.54334 4.98414V4.98927V5.2827H3.83325C2.72868 5.2827 1.83325 6.17813 1.83325 7.2827V12.1664C1.83325 13.271 2.72868 14.1664 3.83325 14.1664H11.6734C12.7779 14.1664 13.6734 13.271 13.6734 12.1664V7.2827C13.6734 6.17814 12.7779 5.2827 11.6734 5.2827H10.9633V5.04584C10.971 4.20269 10.6438 3.39088 10.0536 2.78861C9.4628 2.18573 8.65671 1.84219 7.81263 1.83353V1.83351H7.8075ZM9.96327 5.2827V5.04347V5.03849L9.96329 5.03849C9.96905 4.45956 9.74464 3.90204 9.33941 3.48854C8.93475 3.07562 8.38285 2.84007 7.80481 2.83351H7.75331H7.74833V2.83348C7.1694 2.82772 6.61188 3.05213 6.19838 3.45736C5.78546 3.86203 5.54991 4.41392 5.54334 4.99196V5.2827H9.96327ZM3.83325 6.2827C3.28097 6.2827 2.83325 6.73042 2.83325 7.2827V12.1664C2.83325 12.7187 3.28097 13.1664 3.83325 13.1664H11.6734C12.2256 13.1664 12.6734 12.7187 12.6734 12.1664V7.2827C12.6734 6.73042 12.2256 6.2827 11.6734 6.2827H3.83325ZM7.75345 10.2027C8.01755 10.2027 8.23164 9.98864 8.23164 9.72454C8.23164 9.46044 8.01755 9.24634 7.75345 9.24634C7.48935 9.24634 7.27525 9.46044 7.27525 9.72454C7.27525 9.98864 7.48935 10.2027 7.75345 10.2027ZM9.23164 9.72454C9.23164 10.5409 8.56983 11.2027 7.75345 11.2027C6.93706 11.2027 6.27525 10.5409 6.27525 9.72454C6.27525 8.90815 6.93706 8.24634 7.75345 8.24634C8.56983 8.24634 9.23164 8.90815 9.23164 9.72454Z"
      />
    </svg>
    <svg
      v-if="this.name == 'comment'"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M5.24127 2.66663H9.65506L11.701 2.66663C12.8056 2.66663 13.701 3.56206 13.701 4.66663V7.66075V8.94657C13.701 10.0511 12.8056 10.9466 11.701 10.9466H11.6263C11.3502 10.9466 11.1263 11.1704 11.1263 11.4466V12.2839C11.1263 12.7048 10.6382 12.9374 10.3114 12.6723L8.45917 11.1699C8.28102 11.0254 8.05861 10.9466 7.82923 10.9466H4.66654C3.56195 10.9466 2.66652 10.0511 2.66654 8.94654L2.66656 7.66075V4.66664C2.66656 3.56207 3.56199 2.66664 4.66656 2.66664L5.24127 2.66663Z"
        stroke-miterlimit="10"
        stroke-linecap="square"
      />
      <path
        d="M5.24121 5.24133H10.3906"
        stroke-miterlimit="10"
        stroke-linecap="round"
      />
      <path
        d="M5.24121 7.08044H8.18374"
        stroke-miterlimit="10"
        stroke-linecap="round"
      />
    </svg>
    <svg
      v-if="this.name == 'chevron-up'"
      class="stroke-0"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 10 6"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M8.61989 6H0.573855C0.0648106 6 -0.192865 5.38126 0.169471 5.02149L4.20831 0.169836C4.43412 -0.0559751 4.79005 -0.0572481 5.01586 0.169836L9.02422 5.02149C9.38528 5.38255 9.1288 6 8.61983 6H8.61989Z"
        fill="#C8CFD5"
      />
    </svg>
    <svg
      v-if="this.name == 'chevron-down'"
      class="stroke-0"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 10 6"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M8.73049 0H0.684451C0.175406 0 -0.0822696 0.618739 0.280066 0.978509L4.3189 5.83016C4.54471 6.05598 4.90064 6.05725 5.12646 5.83016L9.13481 0.978509C9.49588 0.617446 9.2394 0 8.73043 0H8.73049Z"
        fill="#C8CFD5"
      />
    </svg>
    <svg
      v-if="this.name == 'customers'"
      class="stroke-black"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 26 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M20.9996 21H24.4438C24.9961 21 25.4438 20.5523 25.4438 20V16.1969C25.4436 15.9749 25.377 15.758 25.2524 15.5742C25.1279 15.3905 24.9511 15.2482 24.7449 15.1658L20.5874 13.4993C20.3815 13.417 20.205 13.2751 20.0805 13.0918C19.956 12.9085 19.8891 12.6921 19.8885 12.4704V11.4949C20.5624 11.1082 21.1225 10.5509 21.5126 9.87896C21.9027 9.20705 22.1089 8.44426 22.1106 7.66733V5.44522C22.1108 4.66501 21.9056 3.89851 21.5156 3.22276C21.1256 2.54701 20.5646 1.98584 19.8889 1.59566C19.2133 1.20548 18.4469 1.00004 17.6667 1C16.8864 0.999956 16.12 1.20531 15.4443 1.59541"
        stroke-miterlimit="10"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M15.8559 16.2768L11.6984 14.6103C11.4925 14.528 11.316 14.3861 11.1915 14.2028C11.0669 14.0195 11.0001 13.8031 10.9995 13.5814V12.6059C11.6734 12.2192 12.2335 11.6619 12.6236 10.99C13.0137 10.318 13.2199 9.55526 13.2216 8.77833V6.55622C13.2216 5.37754 12.7534 4.24714 11.9199 3.41368C11.0865 2.58023 9.95607 2.112 8.77739 2.112C7.59871 2.112 6.4683 2.58023 5.63485 3.41368C4.8014 4.24714 4.33317 5.37754 4.33317 6.55622V8.77833C4.33486 9.55526 4.54113 10.318 4.9312 10.99C5.32128 11.6619 5.88142 12.2192 6.55528 12.6059V13.5814C6.55514 13.8034 6.48849 14.0203 6.36392 14.2041C6.23936 14.3879 6.06259 14.5301 5.85642 14.6125L1.69885 16.2791C1.49304 16.3613 1.31651 16.5032 1.19198 16.6865C1.06744 16.8699 1.00058 17.0863 1 17.3079V19.9999C1 20.5522 1.44772 20.9999 2 20.9999H15.5548C16.1071 20.9999 16.5548 20.5522 16.5548 19.9999V17.3079C16.5546 17.0859 16.488 16.869 16.3634 16.6852C16.2389 16.5015 16.0621 16.3592 15.8559 16.2768Z"
        stroke-miterlimit="10"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
    <svg
      v-if="this.name == 'dislike'"
      class="stroke-black"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12 24C5.37273 24 0 18.6273 0 12C0 5.37273 5.37273 0 12 0C18.6273 0 24 5.37273 24 12C24 18.6273 18.6273 24 12 24ZM12 1.09091C5.97273 1.09091 1.09091 5.97273 1.09091 12C1.09091 18.0273 5.97273 22.9091 12 22.9091C18.0273 22.9091 22.9091 18.0273 22.9091 12C22.9091 5.97273 18.0273 1.09091 12 1.09091ZM8.18191 8.18182C7.58191 8.18182 7.0911 8.67263 7.0911 9.27263C7.0911 9.87263 7.58191 10.3634 8.18191 10.3634C8.78191 10.3634 9.27273 9.87263 9.27273 9.27263C9.27273 8.67263 8.78191 8.18182 8.18191 8.18182ZM15.8183 8.18182C15.2183 8.18182 14.7275 8.67263 14.7275 9.27263C14.7275 9.87263 15.2183 10.3634 15.8183 10.3634C16.4183 10.3634 16.9091 9.87263 16.9091 9.27263C16.9093 8.67263 16.4183 8.18182 15.8183 8.18182ZM12.0002 13.6364C9.05474 13.6364 6.62747 15.7636 6.10929 18.5455H7.22752C7.71833 16.3636 9.68196 14.7274 12.0002 14.7274C14.3185 14.7274 16.2822 16.3638 16.773 18.5455H17.8912C17.373 15.7636 14.9457 13.6364 12.0003 13.6364H12.0002Z"
        fill="#A6B1B9"
      />
    </svg>
    <svg
      v-if="this.name == 'document'"
      class="stroke-black"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 15 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9.5 1H2.5C1.67157 1 1 1.67157 1 2.5V14.5C1 15.3284 1.67157 16 2.5 16H12.5C13.3284 16 14 15.3284 14 14.5V5.5L9.5 1Z"
        stroke-miterlimit="10"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M4 12.5H11"
        stroke-miterlimit="10"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M4 9.5H11"
        stroke-miterlimit="10"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M4 6.5H6"
        stroke-miterlimit="10"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
    <svg
      v-if="this.name == 'folder'"
      class="stroke-black"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 17 15"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M1 2V12C1 13.1046 1.89543 14 3 14H14C15.1046 14 16 13.1046 16 12V4.5C16 3.94772 15.5523 3.5 15 3.5H8.71689C8.54915 3.5 8.39259 3.41589 8.29999 3.27602L6.94139 1.22398C6.84879 1.08411 6.69223 1 6.52448 1H2C1.44772 1 1 1.44772 1 2Z"
        stroke-miterlimit="10"
        stroke-linecap="square"
      />
    </svg>
    <svg
      v-if="this.name == 'frappedesk'"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 70 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M0 0H11.4921V2.9196H0V0ZM0 16.6V7.44866H10.73V10.3732H3.66813V16.6H0Z"
        fill="#2490EF"
      />
      <path
        d="M23.9024 16.3604H18.4921V0H24.0116C25.5966 0 26.9584 0.327521 28.0965 0.982575C29.24 1.63231 30.1183 2.56695 30.7315 3.78653C31.3447 5.0061 31.6514 6.46532 31.6514 8.1642C31.6514 9.8684 31.3422 11.3329 30.7237 12.5579C30.1104 13.7827 29.2243 14.7228 28.0653 15.3778C26.9116 16.0328 25.5239 16.3604 23.9024 16.3604ZM21.3843 13.796H23.7621C24.8743 13.796 25.802 13.5884 26.5452 13.1729C27.2884 12.7522 27.8471 12.1264 28.2213 11.2956C28.5955 10.4595 28.7825 9.41572 28.7825 8.1642C28.7825 6.91268 28.5955 5.87417 28.2213 5.04871C27.8471 4.2179 27.2935 3.59747 26.5608 3.1874C25.8331 2.77199 24.9288 2.56429 23.8479 2.56429H21.3843V13.796Z"
        fill="black"
      />
      <path
        d="M39.3561 16.6001C38.1555 16.6001 37.1187 16.3445 36.2456 15.8332C35.3777 15.3166 34.7099 14.587 34.242 13.6443C33.7743 12.6964 33.5405 11.5807 33.5405 10.2972C33.5405 9.03499 33.7743 7.92727 34.242 6.97398C34.715 6.01536 35.375 5.26977 36.2222 4.73721C37.0693 4.19932 38.0646 3.93037 39.2079 3.93037C39.946 3.93037 40.6424 4.05287 41.2973 4.29785C41.9572 4.5375 42.5394 4.9103 43.0435 5.41623C43.5528 5.92216 43.953 6.56657 44.2441 7.34943C44.5351 8.12697 44.6805 9.05363 44.6805 10.1294V11.0161H34.8657V9.06695H41.9755C41.9703 8.51308 41.8534 8.02047 41.6247 7.58909C41.396 7.15238 41.0764 6.80888 40.6658 6.55858C40.2605 6.30827 39.7875 6.18313 39.2469 6.18313C38.67 6.18313 38.1633 6.32691 37.7267 6.6145C37.2902 6.89675 36.9498 7.26956 36.7055 7.73288C36.4664 8.19088 36.3443 8.69415 36.3392 9.2427V10.9442C36.3392 11.6579 36.4664 12.2704 36.7211 12.7815C36.9758 13.2876 37.3317 13.6764 37.7892 13.9479C38.2464 14.2141 38.7817 14.3473 39.395 14.3473C39.8057 14.3473 40.1772 14.2887 40.5098 14.1716C40.8425 14.0491 41.1309 13.8707 41.3752 13.6363C41.6194 13.4021 41.804 13.1118 41.9288 12.7656L44.5637 13.0691C44.3974 13.7828 44.0803 14.406 43.6126 14.9385C43.15 15.4657 42.5576 15.8757 41.8351 16.1687C41.1127 16.4563 40.2864 16.6001 39.3561 16.6001Z"
        fill="black"
      />
      <path
        d="M56.3127 7.33345L53.7401 7.62104C53.6674 7.35476 53.5401 7.10445 53.3581 6.87013C53.1815 6.63581 52.9424 6.44674 52.6409 6.30295C52.3395 6.15916 51.9705 6.08725 51.534 6.08725C50.9467 6.08725 50.4529 6.21774 50.0528 6.4787C49.6578 6.73965 49.4628 7.07783 49.4681 7.49322C49.4628 7.85004 49.5902 8.14029 49.85 8.36397C50.1151 8.58765 50.5517 8.77138 51.1597 8.91517L53.2023 9.36253C54.3352 9.61283 55.1772 10.0096 55.7281 10.5528C56.2842 11.096 56.5648 11.807 56.57 12.6858C56.5648 13.4579 56.3439 14.1396 55.9073 14.7307C55.476 15.3166 54.8757 15.7745 54.1066 16.1048C53.3373 16.435 52.4538 16.6001 51.456 16.6001C49.9903 16.6001 48.8107 16.2858 47.9166 15.6574C47.0227 15.0237 46.4901 14.1423 46.3186 13.0133L49.0705 12.7416C49.1952 13.2955 49.4603 13.7135 49.8657 13.9958C50.2711 14.278 50.7986 14.4193 51.4482 14.4193C52.1187 14.4193 52.6565 14.278 53.0618 13.9958C53.4725 13.7135 53.6778 13.3648 53.6778 12.9494C53.6778 12.5979 53.5452 12.3076 53.2801 12.0785C53.0203 11.8496 52.615 11.6738 52.064 11.5514L50.0215 11.112C48.873 10.867 48.0232 10.4543 47.4724 9.87378C46.9214 9.28797 46.6486 8.5477 46.6539 7.653C46.6486 6.89675 46.8487 6.2417 47.2541 5.68784C47.6646 5.12864 48.2338 4.69727 48.9614 4.39371C49.6942 4.08482 50.5386 3.93037 51.495 3.93037C52.8982 3.93037 54.0026 4.2366 54.8081 4.84905C55.6189 5.4615 56.1205 6.28963 56.3127 7.33345Z"
        fill="black"
      />
      <path
        d="M61.25 12.5099L61.2423 9.01896H61.6944L65.9976 4.09008H69.2953L64.002 10.1294H63.4173L61.25 12.5099ZM58.6774 16.3604V0H61.4994V16.3604H58.6774ZM66.1925 16.3604L62.2947 10.7764L64.1968 8.73936L69.5681 16.3604H66.1925Z"
        fill="black"
      />
    </svg>
    <svg
      v-if="this.name == 'knowledge-base'"
      class="stroke-black"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M14.2127 7.78706L18.0709 3.92887"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M14.2127 14.2127L18.0709 18.0708"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M7.78726 14.2127L3.92908 18.0708"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M7.78726 7.78706L3.92908 3.92887"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M11 21C16.5228 21 21 16.5228 21 11C21 5.47715 16.5228 1 11 1C5.47715 1 1 5.47715 1 11C1 16.5228 5.47715 21 11 21Z"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M11 15.5454C13.5103 15.5454 15.5454 13.5103 15.5454 10.9999C15.5454 8.48952 13.5103 6.45445 11 6.45445C8.48958 6.45445 6.45451 8.48952 6.45451 10.9999C6.45451 13.5103 8.48958 15.5454 11 15.5454Z"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
    <svg
      v-if="this.name == 'like'"
      class="stroke-black"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12 24C5.37273 24 0 18.6273 0 12C0 5.37273 5.37273 0 12 0C18.6273 0 24 5.37273 24 12C24 18.6273 18.6273 24 12 24ZM12 1.09091C5.97273 1.09091 1.09091 5.97273 1.09091 12C1.09091 18.0273 5.97273 22.9091 12 22.9091C18.0273 22.9091 22.9091 18.0273 22.9091 12C22.9091 5.97273 18.0273 1.09091 12 1.09091ZM8.18191 8.18182C7.58191 8.18182 7.0911 8.67263 7.0911 9.27263C7.0911 9.87263 7.58191 10.3634 8.18191 10.3634C8.78191 10.3634 9.27273 9.87263 9.27273 9.27263C9.27273 8.67263 8.78191 8.18182 8.18191 8.18182ZM15.8183 8.18182C15.2183 8.18182 14.7275 8.67263 14.7275 9.27263C14.7275 9.87263 15.2183 10.3634 15.8183 10.3634C16.4183 10.3634 16.9091 9.87263 16.9091 9.27263C16.9093 8.67263 16.4183 8.18182 15.8183 8.18182ZM12.0002 18.5455C9.05474 18.5455 6.62747 16.4182 6.10929 13.6364H7.22752C7.71833 15.8182 9.68196 17.4544 12.0002 17.4544C14.3185 17.4544 16.2822 15.818 16.773 13.6364H17.8912C17.373 16.4182 14.9456 18.5455 12.0002 18.5455Z"
        fill="#A6B1B9"
      />
    </svg>
    <svg
      v-if="this.name == 'reports'"
      class="stroke-black"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M20.0001 1H15.3334C14.7811 1 14.3334 1.44772 14.3334 2V20.9998H20.0001C20.5523 20.9998 21.0001 20.552 21.0001 19.9998V2C21.0001 1.44772 20.5523 1 20.0001 1Z"
        stroke-miterlimit="10"
        stroke-linecap="square"
      />
      <path
        d="M13.3333 6.71417H8.66661C8.11432 6.71417 7.66661 7.16189 7.66661 7.71417V20.9997H14.3333V7.71417C14.3333 7.16189 13.8856 6.71417 13.3333 6.71417Z"
        stroke-miterlimit="10"
        stroke-linecap="square"
      />
      <path
        d="M6.66667 12.6667H2C1.44772 12.6667 1 13.1144 1 13.6667V19.9999C1 20.5522 1.44771 20.9999 2 20.9999H7.66667V13.6667C7.66667 13.1144 7.21895 12.6667 6.66667 12.6667Z"
        stroke-miterlimit="10"
        stroke-linecap="square"
      />
    </svg>
    <svg
      v-if="this.name == 'settings'"
      class="stroke-black"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 26 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12.9996 16.2729C14.8071 16.2729 16.2724 14.8076 16.2724 13.0001C16.2724 11.1927 14.8071 9.72742 12.9996 9.72742C11.1922 9.72742 9.7269 11.1927 9.7269 13.0001C9.7269 14.8076 11.1922 16.2729 12.9996 16.2729Z"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M21.0727 16.2727C20.9275 16.6018 20.8842 16.9668 20.9484 17.3206C21.0125 17.6745 21.1812 18.0011 21.4327 18.2582L21.4982 18.3236C21.701 18.5263 21.862 18.7669 21.9718 19.0318C22.0816 19.2966 22.1381 19.5805 22.1381 19.8673C22.1381 20.154 22.0816 20.4379 21.9718 20.7028C21.862 20.9676 21.701 21.2083 21.4982 21.4109C21.2956 21.6138 21.0549 21.7747 20.7901 21.8845C20.5252 21.9943 20.2413 22.0508 19.9545 22.0508C19.6678 22.0508 19.3839 21.9943 19.119 21.8845C18.8542 21.7747 18.6135 21.6138 18.4109 21.4109L18.3455 21.3455C18.0884 21.094 17.7618 20.9253 17.4079 20.8611C17.054 20.7969 16.689 20.8402 16.36 20.9855C16.0373 21.1237 15.7622 21.3534 15.5683 21.646C15.3745 21.9387 15.2705 22.2817 15.2691 22.6327V22.8182C15.2691 23.3968 15.0392 23.9518 14.6301 24.361C14.2209 24.7701 13.6659 25 13.0873 25C12.5086 25 11.9537 24.7701 11.5445 24.361C11.1353 23.9518 10.9055 23.3968 10.9055 22.8182V22.72C10.897 22.3589 10.7801 22.0087 10.57 21.7149C10.3599 21.4212 10.0663 21.1974 9.72727 21.0727C9.39824 20.9275 9.03324 20.8842 8.67936 20.9484C8.32547 21.0125 7.99892 21.1812 7.74182 21.4327L7.67636 21.4982C7.47373 21.701 7.2331 21.862 6.96823 21.9718C6.70336 22.0816 6.41945 22.1381 6.13273 22.1381C5.846 22.1381 5.56209 22.0816 5.29722 21.9718C5.03235 21.862 4.79172 21.701 4.58909 21.4982C4.38623 21.2956 4.2253 21.0549 4.11551 20.7901C4.00571 20.5252 3.94919 20.2413 3.94919 19.9545C3.94919 19.6678 4.00571 19.3839 4.11551 19.119C4.2253 18.8542 4.38623 18.6135 4.58909 18.4109L4.65455 18.3455C4.90604 18.0884 5.07475 17.7618 5.13891 17.4079C5.20308 17.054 5.15976 16.689 5.01455 16.36C4.87626 16.0373 4.64664 15.7622 4.35396 15.5683C4.06128 15.3745 3.71831 15.2705 3.36727 15.2691H3.18182C2.60316 15.2691 2.04821 15.0392 1.63904 14.6301C1.22987 14.2209 1 13.6659 1 13.0873C1 12.5086 1.22987 11.9537 1.63904 11.5445C2.04821 11.1353 2.60316 10.9055 3.18182 10.9055H3.28C3.64108 10.897 3.99128 10.7801 4.28505 10.57C4.57883 10.3599 4.8026 10.0663 4.92727 9.72727C5.07249 9.39824 5.11581 9.03324 5.05164 8.67936C4.98748 8.32547 4.81877 7.99892 4.56727 7.74182L4.50182 7.67636C4.29896 7.47373 4.13803 7.2331 4.02823 6.96823C3.91843 6.70336 3.86192 6.41945 3.86192 6.13273C3.86192 5.846 3.91843 5.56209 4.02823 5.29722C4.13803 5.03235 4.29896 4.79172 4.50182 4.58909C4.70445 4.38623 4.94508 4.2253 5.20995 4.11551C5.47482 4.00571 5.75873 3.94919 6.04545 3.94919C6.33218 3.94919 6.61609 4.00571 6.88096 4.11551C7.14583 4.2253 7.38646 4.38623 7.58909 4.58909L7.65455 4.65455C7.91165 4.90604 8.2382 5.07475 8.59209 5.13891C8.94597 5.20308 9.31096 5.15976 9.64 5.01455H9.72727C10.0499 4.87626 10.3251 4.64664 10.5189 4.35396C10.7128 4.06128 10.8168 3.71831 10.8182 3.36727V3.18182C10.8182 2.60316 11.0481 2.04821 11.4572 1.63904C11.8664 1.22987 12.4213 1 13 1C13.5787 1 14.1336 1.22987 14.5428 1.63904C14.9519 2.04821 15.1818 2.60316 15.1818 3.18182V3.28C15.1832 3.63104 15.2872 3.97401 15.4811 4.26669C15.6749 4.55937 15.9501 4.78899 16.2727 4.92727C16.6018 5.07249 16.9668 5.11581 17.3206 5.05164C17.6745 4.98748 18.0011 4.81877 18.2582 4.56727L18.3236 4.50182C18.5263 4.29896 18.7669 4.13803 19.0318 4.02823C19.2966 3.91843 19.5805 3.86192 19.8673 3.86192C20.154 3.86192 20.4379 3.91843 20.7028 4.02823C20.9676 4.13803 21.2083 4.29896 21.4109 4.50182C21.6138 4.70445 21.7747 4.94508 21.8845 5.20995C21.9943 5.47482 22.0508 5.75873 22.0508 6.04545C22.0508 6.33218 21.9943 6.61609 21.8845 6.88096C21.7747 7.14583 21.6138 7.38646 21.4109 7.58909L21.3455 7.65455C21.094 7.91165 20.9253 8.2382 20.8611 8.59209C20.7969 8.94597 20.8402 9.31096 20.9855 9.64V9.72727C21.1237 10.0499 21.3534 10.3251 21.646 10.5189C21.9387 10.7128 22.2817 10.8168 22.6327 10.8182H22.8182C23.3968 10.8182 23.9518 11.0481 24.361 11.4572C24.7701 11.8664 25 12.4213 25 13C25 13.5787 24.7701 14.1336 24.361 14.5428C23.9518 14.9519 23.3968 15.1818 22.8182 15.1818H22.72C22.369 15.1832 22.026 15.2872 21.7333 15.4811C21.4406 15.6749 21.211 15.9501 21.0727 16.2727V16.2727Z"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
    <svg
      v-if="this.name == 'ticket'"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 19 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M1.24749 11.3931C0.993646 11.6469 0.993647 12.0585 1.24749 12.3123L2.554 13.6189C2.68907 13.7539 2.90806 13.7539 3.04313 13.6189C3.75088 12.9111 4.89201 12.9108 5.59897 13.6177C6.30603 14.3248 6.30568 15.4657 5.59783 16.1736C5.46276 16.3086 5.46276 16.5276 5.59783 16.6627L6.90434 17.9692C7.15818 18.223 7.56974 18.223 7.82358 17.9692L18.4698 7.32293C18.7237 7.06909 18.7237 6.65754 18.4698 6.4037L17.1633 5.09718C17.0283 4.96211 16.8093 4.96211 16.6742 5.09718C15.9663 5.80504 14.8254 5.80538 14.1184 5.09832C13.4114 4.39136 13.4117 3.25024 14.1195 2.54248C14.2546 2.40741 14.2546 2.18843 14.1195 2.05336L12.813 0.746841C12.5591 0.493 12.1476 0.493001 11.8937 0.746841L1.24749 11.3931ZM2.01316 12.1C1.87648 11.9633 1.87648 11.7417 2.01316 11.605L8.64421 4.97396C8.78089 4.83728 9.0025 4.83728 9.13918 4.97396L14.2424 10.0772C14.3791 10.2139 14.3791 10.4355 14.2424 10.5722L7.61138 17.2032C7.4747 17.3399 7.25309 17.3399 7.1164 17.2032L6.51854 16.6054C6.39391 16.4807 6.37719 16.2669 6.47925 16.084C7.0057 15.1402 6.89265 13.9333 6.0878 13.1285C5.28308 12.3238 4.07326 12.2066 3.12843 12.7327C2.94517 12.8347 2.73121 12.818 2.60654 12.6934L2.01316 12.1ZM9.62823 4.48492C9.49154 4.34823 9.49155 4.12663 9.62823 3.98994L12.1056 1.51257C12.2423 1.37588 12.4639 1.37588 12.6006 1.51257L13.1939 2.10593C13.3186 2.23059 13.3353 2.44456 13.2333 2.62782C12.7072 3.57259 12.8243 4.78246 13.6291 5.58718C14.4339 6.39203 15.6408 6.50509 16.5845 5.97863C16.7675 5.87657 16.9813 5.89329 17.1059 6.01792L17.7038 6.61578C17.8405 6.75247 17.8405 6.97408 17.7038 7.11076L15.2264 9.58813C15.0897 9.72482 14.8681 9.72482 14.7314 9.58813L9.62823 4.48492Z"
        fill="#74808B"
        stroke="#74808B"
        stroke-width="0.3"
      />
    </svg>
    <svg
      v-if="this.name == 'time'"
      class="stroke-black"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 17 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9.00034 5.0118C9.00034 4.73566 8.77648 4.5118 8.50034 4.5118C8.2242 4.5118 8.00034 4.73566 8.00034 5.0118H9.00034ZM8.50034 8.50017H8.00034C8.00034 8.63277 8.05301 8.75995 8.14678 8.85371L8.50034 8.50017ZM10.6464 11.3535C10.8417 11.5488 11.1583 11.5488 11.3535 11.3535C11.5488 11.1583 11.5488 10.8417 11.3536 10.6464L10.6464 11.3535ZM15.5 8.5C15.5 12.366 12.366 15.5 8.5 15.5V16.5C12.9183 16.5 16.5 12.9183 16.5 8.5H15.5ZM8.5 15.5C4.63401 15.5 1.5 12.366 1.5 8.5H0.5C0.5 12.9183 4.08172 16.5 8.5 16.5V15.5ZM1.5 8.5C1.5 4.63401 4.63401 1.5 8.5 1.5V0.5C4.08172 0.5 0.5 4.08172 0.5 8.5H1.5ZM8.5 1.5C12.366 1.5 15.5 4.63401 15.5 8.5H16.5C16.5 4.08172 12.9183 0.5 8.5 0.5V1.5ZM8.00034 5.0118V8.50017H9.00034V5.0118H8.00034ZM8.14678 8.85371L10.6464 11.3535L11.3536 10.6464L8.8539 8.14663L8.14678 8.85371Z"
        fill="#A6B1B9"
      />
    </svg>
    <svg
      v-if="this.name == 'filter'"
      class="stroke-black"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M2 4H14"
        stroke-miterlimit="10"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M4 8H12"
        stroke-miterlimit="10"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M6.5 12H9.5"
        stroke-miterlimit="10"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
    <svg
      v-if="this.name == 'sort-ascending'"
      class="stroke-black"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M1.75 3.25H10.75"
        stroke="#505A62"
        stroke-miterlimit="10"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M1.75 7.25H7.75"
        stroke="#505A62"
        stroke-miterlimit="10"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M1.75 11.25H5.75"
        stroke="#505A62"
        stroke-miterlimit="10"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M14.25 7.75L12.25 5.75L10.25 7.75"
        stroke="#505A62"
        stroke-miterlimit="10"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M12.25 11.75L12.25 5.75"
        stroke="#505A62"
        stroke-miterlimit="10"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
    <svg
      v-if="this.name == 'select'"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 12 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M4.5 3.63636L6.13636 2L7.77273 3.63636M4.5 8.36364L6.13636 10L7.77273 8.36364"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
    <svg
      v-if="this.name == 'empty-list'"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 78 85"
      fill="none"
    >
      <rect
        x="13"
        y="12"
        width="64"
        height="72"
        rx="7"
        stroke="#A6B1B9"
        stroke-width="2"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M8 2H58C61.3137 2 64 4.68629 64 8V9H66V8C66 3.58172 62.4183 0 58 0H8C3.58172 0 0 3.58172 0 8V66C0 70.4183 3.58172 74 8 74H10V72H8C4.68629 72 2 69.3137 2 66V8C2 4.68629 4.68629 2 8 2Z"
        fill="#A6B1B9"
      />
      <path
        d="M42 31H66"
        stroke="#A6B1B9"
        stroke-width="2"
        stroke-linecap="round"
      />
      <path
        d="M42 51H66"
        stroke="#A6B1B9"
        stroke-width="2"
        stroke-linecap="round"
      />
      <path
        d="M42 25H55"
        stroke="#A6B1B9"
        stroke-width="2"
        stroke-linecap="round"
      />
      <path
        d="M42 45H55"
        stroke="#A6B1B9"
        stroke-width="2"
        stroke-linecap="round"
      />
      <rect
        x="24"
        y="23"
        width="10"
        height="10"
        rx="2"
        stroke="#2D95F0"
        stroke-width="2"
      />
      <rect
        x="24"
        y="43"
        width="10"
        height="10"
        rx="2"
        stroke="#2D95F0"
        stroke-width="2"
      />
    </svg>
    <svg
      v-if="this.name == 'bold'"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 17 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M7.11552 8.48467V11.1533H8.63755C9.89361 11.1533 10.5216 10.7085 10.5216 9.81896C10.5216 8.92944 9.89361 8.48467 8.63755 8.48467H7.11552ZM7.11552 4.6092V6.87548H8.24195C8.85881 6.87548 9.2991 6.7682 9.56283 6.55364C9.82656 6.33461 9.95843 6.06865 9.95843 5.75575C9.95843 5.43838 9.82433 5.16794 9.55613 4.94444C9.2924 4.72094 8.85434 4.6092 8.24195 4.6092H7.11552ZM5.31858 3H8.24195C8.65766 3 9.03761 3.03129 9.3818 3.09387C9.72599 3.15645 10.0165 3.23914 10.2534 3.34195C10.4904 3.44029 10.7004 3.56322 10.8837 3.71073C11.067 3.85377 11.2123 4.00128 11.3195 4.15326C11.4268 4.30077 11.514 4.46392 11.581 4.64272C11.6481 4.81705 11.6928 4.9802 11.7151 5.13218C11.742 5.27969 11.7554 5.43167 11.7554 5.58812C11.7554 6.07535 11.6257 6.47094 11.3665 6.7749C11.1117 7.07439 10.7742 7.28895 10.354 7.41858C10.7831 7.52139 11.1519 7.72031 11.4603 8.01533C11.7732 8.31034 11.9945 8.6143 12.1241 8.9272C12.2538 9.2401 12.3186 9.53736 12.3186 9.81896C12.3186 10.0559 12.294 10.2928 12.2448 10.5297C12.2001 10.7621 12.1018 11.0192 11.9498 11.3008C11.7978 11.5779 11.6011 11.8215 11.3598 12.0316C11.1184 12.2372 10.7787 12.4116 10.3406 12.5546C9.90702 12.6932 9.40638 12.7625 8.8387 12.7625H5.31858V3Z"
        fill="#1F272E"
      />
    </svg>
    <svg
      v-if="this.name == 'text-formatting'"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 17 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.97781 4.15745L7.95202 7.12282H9.99771L8.97781 4.15745ZM9.8857 2.2002L12.969 10.7838H11.1355L10.3337 8.53181C10.3337 8.53181 9.4239 8.53181 7.6042 8.53181L6.80243 10.7838H4.96897L8.05224 2.2002H9.8857Z"
        fill="#1F272E"
      />
      <path
        d="M2.81858 13.5C2.81858 13.2239 3.04244 13 3.31858 13H14.3186C14.5947 13 14.8186 13.2239 14.8186 13.5V13.5C14.8186 13.7761 14.5947 14 14.3186 14H3.31858C3.04244 14 2.81858 13.7761 2.81858 13.5V13.5Z"
        fill="#1F272E"
      />
    </svg>
    <svg
      v-if="this.name == 'bullet-list'"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 17 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6.1514 3H14.8186"
        stroke="#1F272E"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M6.1514 7.80005H14.8186"
        stroke="#1F272E"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M6.1514 12.6001H14.8186"
        stroke="#1F272E"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M2.81857 3H2.82457"
        stroke="#1F272E"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M2.81857 7.80005H2.82457"
        stroke="#1F272E"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M2.81857 12.6001H2.82457"
        stroke="#1F272E"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
    <svg
      v-if="this.name == 'clear-formatting'"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 17 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M5.48459 4.6665H13.8179"
        stroke="#1F272E"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M7.98459 12.1665L10.7763 4.6665"
        stroke="#1F272E"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M3.81857 3L13.8186 13"
        stroke="#FBFBFB"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M4.65256 4.6665L12.9859 12.9998"
        stroke="#1F272E"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
    <svg
      v-if="this.name == 'code'"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 17 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10.8176 3L6.8176 13.4"
        stroke="#1F272E"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M5.21857 5.3999L2.81857 7.7999L5.21857 10.1999"
        stroke="#1F272E"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M12.4184 5.3999L14.8184 7.7999L12.4184 10.1999"
        stroke="#1F272E"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
    <svg
      v-if="this.name == 'decrease-indent'"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 17 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.01877 7.8999H14.1388"
        stroke="#1F272E"
        stroke-miterlimit="10"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M4.01877 3H14.0188"
        stroke="#1F272E"
        stroke-miterlimit="10"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M4.01877 12.7998H14.0188"
        stroke="#1F272E"
        stroke-miterlimit="10"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M6.31857 6.25392L6.31857 9.54589C6.31857 9.88502 5.92303 10.0703 5.6625 9.85317L3.68732 8.20719C3.49542 8.04727 3.49542 7.75253 3.68732 7.59261L5.6625 5.94663C5.92303 5.72952 6.31857 5.91478 6.31857 6.25392Z"
        fill="#1F272E"
      />
    </svg>
    <svg
      v-if="this.name == 'image'"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 17 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9.14357 14.1333C7.67325 14.1333 5.74817 14.1333 4.31751 14.1333C3.21294 14.1333 2.31857 13.2379 2.31857 12.1333V4C2.31857 2.89543 3.214 2 4.31857 2H13.3186C14.4231 2 15.3186 2.89543 15.3186 4V8.37"
        stroke="#1F272E"
        stroke-miterlimit="10"
        stroke-linecap="square"
      />
      <path
        d="M7.27946 6.07369C7.27946 6.37193 7.0377 6.61369 6.73946 6.61369C6.44123 6.61369 6.19946 6.37193 6.19946 6.07369C6.19946 5.77546 6.44123 5.53369 6.73946 5.53369C7.0377 5.53369 7.27946 5.77546 7.27946 6.07369Z"
        stroke="#1F272E"
        stroke-miterlimit="10"
        stroke-linecap="square"
      />
      <path
        d="M5.43875 11.2735L7.51875 8.67353L9.59875 10.2335L12.1988 7.11353"
        stroke="#1F272E"
        stroke-miterlimit="10"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
    <svg
      v-if="this.name == 'increase-indent'"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 17 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.01877 7.8999H14.1386"
        stroke="#1F272E"
        stroke-miterlimit="10"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M4.01877 3H14.0188"
        stroke="#1F272E"
        stroke-miterlimit="10"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M4.01877 12.7998H14.0188"
        stroke="#1F272E"
        stroke-miterlimit="10"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M3.31857 9.54589L3.31857 6.25392C3.31857 5.91478 3.71412 5.72952 3.97465 5.94663L5.94983 7.59261C6.14173 7.75253 6.14173 8.04727 5.94983 8.20719L3.97465 9.85317C3.71412 10.0703 3.31857 9.88502 3.31857 9.54589Z"
        fill="#1F272E"
      />
    </svg>
    <svg
      v-if="this.name == 'italic'"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 17 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clip-path="url(#clip0_3327_18536)">
        <path
          d="M6.31858 12.2963L6.49099 11.4465H7.63631L9.00331 4.54995H7.85799L8.0304 3.7002H11.3186L11.1462 4.54995H10.0008L8.63385 11.4465H9.77917L9.60676 12.2963H6.31858Z"
          fill="#1F272E"
        />
      </g>
      <defs>
        <clipPath id="clip0_3327_18536">
          <rect
            width="16"
            height="16"
            fill="white"
            transform="translate(0.818581)"
          />
        </clipPath>
      </defs>
    </svg>
    <svg
      v-if="this.name == 'right-align'"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
      class="feather feather-align-right feather feather-align-right"
    >
      <line x1="21" y1="10" x2="7" y2="10"></line>
      <line x1="21" y1="6" x2="3" y2="6"></line>
      <line x1="21" y1="14" x2="3" y2="14"></line>
      <line x1="21" y1="18" x2="7" y2="18"></line>
    </svg>
    <svg
      v-if="this.name == 'center-align'"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
      class="feather feather-align-center feather feather-align-center"
    >
      <line x1="18" y1="10" x2="6" y2="10"></line>
      <line x1="21" y1="6" x2="3" y2="6"></line>
      <line x1="21" y1="14" x2="3" y2="14"></line>
      <line x1="18" y1="18" x2="6" y2="18"></line>
    </svg>
    <svg
      v-if="this.name == 'left-align'"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
      class="feather feather-align-left feather feather-align-left"
    >
      <line x1="17" y1="10" x2="3" y2="10"></line>
      <line x1="21" y1="6" x2="3" y2="6"></line>
      <line x1="21" y1="14" x2="3" y2="14"></line>
      <line x1="17" y1="18" x2="3" y2="18"></line>
    </svg>
    <svg
      v-if="this.name == 'link-url'"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 17 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6.09201 10.7268L11.5463 5.27246"
        stroke="#1F272E"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M7.72766 4.72735L9.55158 2.90344C10.1303 2.32496 10.915 2 11.7333 2C12.5515 2 13.3363 2.32496 13.915 2.90344V2.90344C14.4935 3.48215 14.8185 4.2669 14.8185 5.08516C14.8185 5.90341 14.4935 6.68817 13.915 7.26687L12.0911 9.09079"
        stroke="#1F272E"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M5.54593 6.90918L3.72201 8.7331C3.14354 9.31181 2.81857 10.0966 2.81857 10.9148C2.81857 11.7331 3.14354 12.5178 3.72201 13.0965V13.0965C4.30072 13.675 5.08548 14 5.90373 14C6.72198 14 7.50674 13.675 8.08545 13.0965L9.90937 11.2726"
        stroke="#1F272E"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
    <svg
      v-if="this.name == 'numbered-list'"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 17 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6.73553 3.41895H14.8186"
        stroke="#1F272E"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M6.7355 7.89551H14.8186"
        stroke="#1F272E"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M6.7355 12.3721H14.8186"
        stroke="#1F272E"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M3.69943 4.7998V2.61035H3.68576L2.98068 3.08691V2.46973L3.69748 1.98145H4.41232V4.7998H3.69943Z"
        fill="#1F272E"
      />
      <path
        d="M2.83419 7.34863C2.83419 7.06999 2.9351 6.84408 3.13692 6.6709C3.33874 6.49772 3.60372 6.41113 3.93184 6.41113C4.24955 6.41113 4.50476 6.48861 4.69747 6.64355C4.89148 6.7985 4.98848 7.00033 4.98848 7.24902C4.98848 7.31934 4.97742 7.3903 4.95528 7.46191C4.93315 7.53223 4.9071 7.59473 4.87716 7.64941C4.84721 7.7028 4.80619 7.76139 4.75411 7.8252C4.70333 7.8877 4.65971 7.93783 4.62325 7.97559C4.58679 8.01204 4.53926 8.05827 4.48067 8.11426L3.80684 8.73535V8.74902H5.02364V9.2998H2.8752V8.82129L3.97481 7.80957C4.0933 7.69629 4.17598 7.60059 4.22286 7.52246C4.27104 7.44303 4.29512 7.3597 4.29512 7.27246C4.29512 7.18132 4.25932 7.10514 4.1877 7.04395C4.11609 6.98145 4.02299 6.9502 3.90841 6.9502C3.78731 6.9502 3.68705 6.98796 3.60762 7.06348C3.5295 7.1377 3.49044 7.23275 3.49044 7.34863V7.3623H2.83419V7.34863Z"
        fill="#1F272E"
      />
      <path
        d="M3.56661 12.6064V12.1123H3.90841C4.0295 12.1123 4.1265 12.0817 4.19942 12.0205C4.27234 11.958 4.3088 11.876 4.3088 11.7744C4.3088 11.6715 4.27299 11.5908 4.20137 11.5322C4.12976 11.4736 4.03015 11.4443 3.90255 11.4443C3.78015 11.4443 3.68054 11.4782 3.60372 11.5459C3.5269 11.6136 3.48653 11.7021 3.48262 11.8115H2.83419C2.8394 11.5394 2.94226 11.3213 3.14278 11.1572C3.3433 10.9932 3.60697 10.9111 3.9338 10.9111C4.2476 10.9111 4.4989 10.9808 4.6877 11.1201C4.87781 11.2594 4.97286 11.445 4.97286 11.6768C4.97286 11.8447 4.91882 11.9873 4.81075 12.1045C4.70268 12.2217 4.5614 12.2959 4.38692 12.3271V12.3428C4.59786 12.3623 4.76518 12.432 4.88887 12.5518C5.01387 12.6715 5.07637 12.8271 5.07637 13.0186C5.07637 13.2764 4.96895 13.4827 4.75411 13.6377C4.54057 13.7926 4.25801 13.8701 3.90645 13.8701C3.57703 13.8701 3.3101 13.7874 3.10567 13.6221C2.90255 13.4567 2.79382 13.2367 2.7795 12.9619H3.46309C3.4696 13.0687 3.51322 13.1533 3.59395 13.2158C3.67468 13.277 3.78275 13.3076 3.91817 13.3076C4.04578 13.3076 4.14929 13.2757 4.22872 13.2119C4.30945 13.1468 4.34981 13.0622 4.34981 12.958C4.34981 12.8486 4.31075 12.7627 4.23262 12.7002C4.1545 12.6377 4.04708 12.6064 3.91036 12.6064H3.56661Z"
        fill="#1F272E"
      />
    </svg>
    <svg
      v-if="this.name == 'quote'"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 17 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M7.25573 3.3121L4.33894 3.3121C4.0513 3.3121 3.81809 3.54507 3.81809 3.83296L3.81809 7.79145C3.81809 8.07909 4.0513 8.31231 4.33894 8.31231L6.71987 8.31231C6.60797 10.2065 5.86305 11.4145 4.45232 11.9977C4.18655 12.1078 4.06015 12.4123 4.17002 12.6783C4.28039 12.944 4.58558 13.0699 4.85059 12.9606C6.79209 12.1577 7.77658 10.4186 7.77658 7.79145L7.77658 3.83296C7.77658 3.54507 7.54337 3.3121 7.25573 3.3121ZM13.2977 3.31211L10.3809 3.3121C10.0933 3.3121 9.86008 3.54507 9.86008 3.83296L9.86008 7.79145C9.86008 8.0791 10.0933 8.31231 10.3809 8.31231L12.7619 8.31231C12.65 10.2065 11.905 11.4145 10.4943 11.9977C10.2285 12.1078 10.1021 12.4123 10.212 12.6783C10.3224 12.944 10.6268 13.0699 10.8926 12.9606C12.8341 12.1577 13.8186 10.4186 13.8186 7.79145L13.8186 3.83296C13.8186 3.54507 13.5854 3.31211 13.2977 3.31211Z"
        fill="#1F272E"
      />
    </svg>
    <svg
      v-if="this.name == 'add-response'"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 16 15"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12.3761 0.500004H7.3449C7.03225 0.500004 6.74342 0.666779 6.58717 0.937459C6.43092 1.20814 6.43092 1.54171 6.58717 1.81237C6.74342 2.08321 7.03228 2.24998 7.3449 2.24998H12.3761C12.6082 2.24998 12.8306 2.34214 12.9948 2.50617C13.1588 2.67035 13.251 2.89282 13.251 3.12491V10.1248C13.251 10.3569 13.1588 10.5795 12.9948 10.7436C12.8306 10.9076 12.6082 10.9999 12.3761 10.9999H5.22539C4.76107 10.9994 4.31568 11.1836 3.98717 11.5117L2.75137 12.7498V9.68738C2.75137 9.37473 2.58459 9.0859 2.31376 8.92965C2.04307 8.77341 1.70951 8.77341 1.43885 8.92965C1.16819 9.0859 1.00139 9.37476 1.00139 9.68738V12.6907C0.992543 13.3333 1.32579 13.9322 1.8763 14.2636C2.21001 14.4572 2.59832 14.5352 2.98086 14.4853C3.36339 14.4356 3.71878 14.2607 3.99158 13.988L5.22539 12.7498H12.3761C13.0723 12.7498 13.7399 12.4733 14.2321 11.9811C14.7245 11.4887 15.001 10.8211 15.001 10.1249V3.12496C15.001 2.42872 14.7246 1.76115 14.2321 1.2689C13.7399 0.77652 13.0724 0.5 12.3761 0.5V0.500004Z"
        fill="black"
        stroke="white"
        stroke-width="0.6"
      />
      <path
        d="M3.3335 0.578814C3.12171 0.482686 2.88045 0.474901 2.66304 0.556994C2.44545 0.639087 2.26968 0.804334 2.17414 1.01628L1.11321 3.34383C0.937131 3.72606 0.967951 4.17175 1.19516 4.52592C1.42235 4.88023 1.81448 5.09431 2.23534 5.09369H3.77981L3.04923 6.69933H3.04908C2.94578 6.91157 2.9325 7.15663 3.0123 7.37881C3.09226 7.60097 3.25858 7.7815 3.47359 7.87915C3.68843 7.97681 3.93377 7.98352 4.15381 7.89777C4.37369 7.81202 4.54962 7.64082 4.64162 7.42336L5.70254 5.09367C5.87862 4.71144 5.8478 4.26575 5.6206 3.91158C5.3934 3.55727 5.00127 3.34319 4.58026 3.34381H3.03595L3.76652 1.73817H3.76668C3.86296 1.52699 3.8715 1.28622 3.79017 1.06877C3.709 0.851332 3.54466 0.6751 3.3335 0.578812L3.3335 0.578814Z"
        fill="black"
        stroke="white"
        stroke-width="0.6"
      />
    </svg>
    <svg
      v-if="this.name == 'chevron-down-accordion'"
      class="-all w-3 transform duration-200"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      fill="none"
      stroke="currentColor"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 16 10"
      aria-hidden="true"
    >
      <path
        d="M15 1.2l-7 7-7-7"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
    <svg
      v-if="this.name == 'add-new'"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect :width="24" :height="24" rx="6" fill="#F4F5F6" />
      <path
        d="M12 8V16"
        stroke="#1F272E"
        stroke-miterlimit="10"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M8 12H16"
        stroke="#1F272E"
        stroke-miterlimit="10"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>

    <svg
      v-if="this.name == 'underline'"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 17 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clip-path="url(#clip0_3327_18531)">
        <path
          d="M6.84135 2.2998V7.6767C6.84135 8.26692 7.01448 8.74106 7.36074 9.09913C7.71094 9.45326 8.19688 9.63033 8.81858 9.63033C9.44028 9.63033 9.92623 9.45326 10.2764 9.09913C10.6266 8.74106 10.8017 8.26692 10.8017 7.6767V2.2998H12.3186V7.81245C12.3186 8.75287 12.0018 9.51228 11.3683 10.0907C10.7388 10.6691 9.88884 10.9583 8.81858 10.9583C7.75225 10.9583 6.90234 10.6691 6.26883 10.0907C5.63533 9.51228 5.31858 8.75287 5.31858 7.81245V2.2998H6.84135Z"
          fill="#1F272E"
        />
        <path
          d="M2.81858 13.5C2.81858 13.2239 3.04244 13 3.31858 13H14.3186C14.5947 13 14.8186 13.2239 14.8186 13.5C14.8186 13.7761 14.5947 14 14.3186 14H3.31858C3.04244 14 2.81858 13.7761 2.81858 13.5Z"
          fill="#1F272E"
        />
      </g>
      <defs>
        <clipPath id="clip0_3327_18531">
          <rect
            width="16"
            height="16"
            fill="white"
            transform="translate(0.818581)"
          />
        </clipPath>
      </defs>
    </svg>
    <svg
      v-if="this.name == 'drag-handle'"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      stroke-width="1"
      stroke-linecap="round"
      stroke-linejoin="round"
    >
      <circle cx="12" cy="9" r="1"></circle>
      <circle cx="19" cy="9" r="1"></circle>
      <circle cx="5" cy="9" r="1"></circle>
      <circle cx="12" cy="15" r="1"></circle>
      <circle cx="19" cy="15" r="1"></circle>
      <circle cx="5" cy="15" r="1"></circle>
    </svg>
    <svg
      v-if="this.name == 'list-drag-handle'"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    >
      <line x1="3" y1="15" x2="21" y2="15"></line>
      <line x1="3" y1="9" x2="21" y2="9"></line>
    </svg>
    <svg
      v-if="this.name == 'kb-article'"
      class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 14 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.99465 15.5603H4.36529C4.07229 15.5603 3.83789 15.7947 3.83789 16.0877C3.83789 16.3807 4.07229 16.6151 4.36529 16.6151H8.99465C9.28765 16.6151 9.52205 16.3807 9.52205 16.0877C9.52205 15.7947 9.28765 15.5603 8.99465 15.5603Z"
        fill="#74808B"
      />
      <path
        d="M4.27843 10.4327H9.11289C9.72819 10.4327 10.2263 9.93462 10.2263 9.31932V7.50273C10.2263 6.88744 9.72819 6.38934 9.11289 6.38934H4.27843C3.66314 6.38934 3.16504 6.88744 3.16504 7.50273V9.31932C3.16504 9.93462 3.66314 10.4327 4.27843 10.4327ZM4.19053 7.50273C4.19053 7.47343 4.21983 7.41484 4.27843 7.41484H9.11289C9.14219 7.41484 9.20079 7.44413 9.20079 7.50273V9.31932C9.20079 9.34862 9.17149 9.40722 9.11289 9.40722H4.27843C4.24913 9.40722 4.19053 9.37792 4.19053 9.31932V7.50273Z"
        fill="#74808B"
      />
      <path
        d="M2.10958 1.93632H12.8626C13.1556 1.93632 13.39 1.70192 13.39 1.40893C13.39 1.11593 13.1556 0.881531 12.8626 0.881531H2.10958C0.996193 0.881531 0.0878993 1.73122 0 2.84462C0 2.87392 0 2.90322 0 2.93252V17.2308C0 18.1684 0.820394 18.9302 1.81659 18.9302H11.8078C12.5696 18.9302 13.1849 18.3149 13.1849 17.5531V5.3644C13.1849 4.6026 12.5696 3.98731 11.8078 3.98731H2.05098C1.43569 3.98731 1.05479 3.60641 1.05479 3.02041C1.02549 2.40512 1.49429 1.93632 2.10958 1.93632ZM2.02169 5.0128C2.02169 5.0128 2.05098 5.0128 2.02169 5.0128H11.7785C11.9836 5.0128 12.1301 5.1593 12.1301 5.3644V17.5531C12.1301 17.7582 11.9836 17.9047 11.7785 17.9047H1.78729C1.37709 17.9047 0.996193 17.6117 0.996193 17.2308V4.7784C1.28919 4.9249 1.64079 5.0128 2.02169 5.0128Z"
        fill="#74808B"
      />
    </svg>
    <svg
      v-if="this.name == 'article-reply'"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 15 15"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M7.64624 2.51627V9.06354"
        stroke="black"
        stroke-miterlimit="10"
        stroke-linecap="round"
      />
      <path
        d="M9.30747 0.855011C8.38963 0.855011 7.64622 1.59842 7.64622 2.51626C7.64622 1.59842 6.90281 0.855011 5.98497 0.855011H2.00122C1.44894 0.855011 1.00122 1.30273 1.00122 1.85501V10.4838C1.00122 11.5883 1.89665 12.4838 3.00122 12.4838H5.98497C6.90281 12.4838 7.64622 13.2272 7.64622 14.145C7.64622 13.2272 8.38963 12.4838 9.30747 12.4838H12.2912C13.3958 12.4838 14.2912 11.5883 14.2912 10.4838V1.85501C14.2912 1.30273 13.8435 0.855011 13.2912 0.855011H9.30747Z"
        stroke="black"
        stroke-miterlimit="10"
        stroke-linecap="square"
      />
    </svg>
    <svg
      v-if="this.name == 'kb-articles'"
      :class="this.class"
      :width="18"
      :height="18"
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <line
        x1="11.5"
        y1="5.5"
        x2="15.5"
        y2="5.5"
        stroke="#687178"
        stroke-linecap="round"
      />
      <line
        x1="3.5"
        y1="5.5"
        x2="7.5"
        y2="5.5"
        stroke="#687178"
        stroke-linecap="round"
      />
      <line
        x1="11.5"
        y1="8.5"
        x2="13.5"
        y2="8.5"
        stroke="#687178"
        stroke-linecap="round"
      />
      <line
        x1="3.5"
        y1="8.5"
        x2="7.5"
        y2="8.5"
        stroke="#687178"
        stroke-linecap="round"
      />
      <path
        d="M9.14575 3.0365V11.0625"
        stroke="#687178"
        stroke-miterlimit="10"
        stroke-linecap="round"
      />
      <path
        d="M11.1823 1C10.0571 1 9.14583 1.91132 9.14583 3.03646C9.14583 1.91132 8.23452 1 7.10938 1H2C1.44772 1 1 1.44772 1 2V13.2552C1 14.3598 1.89543 15.2552 3 15.2552H7.10938C8.23452 15.2552 9.14583 16.1665 9.14583 17.2917C9.14583 16.1665 10.0571 15.2552 11.1823 15.2552H15.2917C16.3962 15.2552 17.2917 14.3598 17.2917 13.2552V2C17.2917 1.44772 16.844 1 16.2917 1H11.1823Z"
        stroke="#687178"
        stroke-miterlimit="10"
        stroke-linecap="square"
      />
    </svg>
    <svg
      v-if="this.name == 'customer'"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 17 15"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9.25 7H12.85C13.6901 7 14.1101 7 14.431 7.16349C14.7132 7.3073 14.9427 7.53677 15.0865 7.81901C15.25 8.13988 15.25 8.55992 15.25 9.4V14.5M9.25 7V3.4C9.25 2.55992 9.25 2.13988 9.08651 1.81901C8.9427 1.53677 8.71323 1.3073 8.43099 1.16349C8.11012 1 7.69008 1 6.85 1H4.15C3.30992 1 2.88988 1 2.56901 1.16349C2.28677 1.3073 2.0573 1.53677 1.91349 1.81901C1.75 2.13988 1.75 2.55992 1.75 3.4V14.5M9.25 7V14.5M15.25 14.5H16M15.25 14.5H9.25M9.25 14.5H1.75M1.75 14.5H1M4.375 3.99982H6.625M4.375 6.99982H6.625M4.375 9.99982H6.625"
        stroke="#687178"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
    <svg
      v-if="this.name == 'dashboard'"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 15 15"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9.7549 0.499939H13.4804C14.043 0.499939 14.5 0.95864 14.5 1.50963V13.4902C14.5 14.0412 14.043 14.4999 13.4804 14.4999H9.7549C9.19235 14.4999 8.73534 14.0412 8.73534 13.4902V1.50963C8.73534 0.95864 9.19235 0.499939 9.7549 0.499939ZM1.51955 10.776H5.24511C5.80765 10.776 6.26466 11.2347 6.26466 11.7857V13.4902C6.26466 14.0412 5.80765 14.4999 5.24511 14.4999H1.51955C0.957002 14.4999 0.5 14.0412 0.5 13.4902V11.7857C0.5 11.2347 0.957001 10.776 1.51955 10.776ZM1.51955 0.499939H5.24511C5.80765 0.499939 6.26466 0.95864 6.26466 1.50963V7.40247C6.26466 7.95346 5.80765 8.41216 5.24511 8.41216H1.51955C0.957002 8.41216 0.5 7.95346 0.5 7.40247V1.50963C0.5 0.95864 0.957001 0.499939 1.51955 0.499939Z"
        stroke="#687178"
      />
    </svg>
    <svg
      v-if="this.name == 'corner-up-right'"
      :class="this.class"
      :width="this.width"
      :height="this.height"
      viewBox="0 0 11 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M1 10.6667L10.3333 1.33342"
        stroke="#A6B1B9"
        stroke-miterlimit="10"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M10.3333 8.66671V1.33337H3"
        stroke="#A6B1B9"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
    <svg
      v-if="name == 'arrow-down'"
      :class="class"
      :width="width"
      :height="height"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8 10L12 14L16 10"
        stroke="#4C5A67"
        stroke-miterlimit="10"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  </div>
</template>

<script>
export default {
  name: "CustomIcons",
  props: ["name", "height", "width", "class"],
};
</script>

<style></style>
