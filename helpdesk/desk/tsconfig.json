{
	"compilerOptions": {
		"esModuleInterop": true,
		"allowSyntheticDefaultImports": true,
		"target": "es2022",
		"module": "esnext",
		"moduleResolution":"node",
		"jsx": "preserve",
		"paths": {
			"@/*": ["./src/*"]
		},
		"types": [
			"unplugin-icons/types/vue",
			"vite/client"
		],
	},
	"include": ["src","src/*","src/**/*.d.ts", "src/**/*.ts", "src/**/*.tsx", "src/**/*.js", "src/**/*.jsx","src/**/*.vue"],
	"exclude": ["node_modules"]
}
