{"private": true, "scripts": {"postinstall": "cd desk && yarn install", "dev": "cd desk && yarn dev", "build": "cd desk && yarn build", "disable-workspaces": "sed -i '' 's/\"workspaces\"/\"aworkspaces\"/g' package.json", "enable-workspaces": "sed -i '' 's/\"aworkspaces\"/\"workspaces\"/g' package.json && rm -rf node_modules ./desk/node_modules/ frappe-ui/node_modules/ && yarn install"}, "workspaces": ["desk", "frappe-ui"], "resolutions": {"cheerio": "1.0.0-rc.12", "prosemirror-model": "1.25.1"}}