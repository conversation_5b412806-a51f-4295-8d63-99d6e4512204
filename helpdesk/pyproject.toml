[project]
name = "helpdesk"
authors = [
    { name = "Frappe Technologies Pvt Ltd", email = "<EMAIL>"}
]
description = "Open Source Customer Service Software"
requires-python = ">=3.10"
readme = "README.md"
dynamic = ["version"]
dependencies = [
    # Core dependencies
    "textblob==0.18.0.post0",
]
[build-system]
requires = ["flit_core >=3.4,<4"]
build-backend = "flit_core.buildapi"

[tool.bench.frappe-dependencies]
frappe = ">=16.0.0-dev,<17.0.0"