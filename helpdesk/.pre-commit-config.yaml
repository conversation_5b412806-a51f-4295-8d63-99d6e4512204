exclude: "node_modules|.git"
default_stages: [pre-commit]
fail_fast: false

repos:
  - repo: https://github.com/psf/black
    rev: 22.6.0
    hooks:
      - id: black
        types_or: [python, pyi]

  - repo: https://github.com/PyCQA/flake8
    rev: 5.0.4
    hooks:
      - id: flake8
        additional_dependencies:
          - flake8-bugbear
          - flake8-comprehensions
          - flake8-simplify
        args: ['--config', '.github/helpers/.flake8_strict']

  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v2.6.2
    hooks:
      - id: prettier
        additional_dependencies:
          - prettier@2.6.2
        types: [file]
        types_or: [javascript, vue, html]

  - repo: https://github.com/timothycrosley/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: [--profile, black]

ci:
  autoupdate_schedule: weekly
  skip: []
  submodules: false
