# Since version 2.23 (released in August 2019), git-blame has a feature
# to ignore or bypass certain commits.
#
# This file contains a list of commits that are not likely what you
# are looking for in a blame, such as mass reformatting or renaming.
# You can set this file as a default ignore file for blame by running
# the following command.
#
# $ git config blame.ignoreRevsFile .git-blame-ignore-revs

# bulk formatting
b7fb25fe7c784c4dcf639cf954445ac8452a85d9
353aaae07a5f56c5686caa79ce5d1e2e1544494a
