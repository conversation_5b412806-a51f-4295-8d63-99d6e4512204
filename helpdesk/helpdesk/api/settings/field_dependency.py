import re

import frappe
from frappe import _


@frappe.whitelist()
def get_fields_meta(doctype="HD Ticket", fieldtypes=None):
    """
    Returns the metadata for the given doctype.
    """

    all_fields = frappe.get_meta(doctype).fields
    if not fieldtypes:
        return all_fields
    fields = []
    for field in all_fields:
        if field.fieldtype in fieldtypes:
            fields.append(field)
    return fields


@frappe.whitelist()
def get_field_dependency(name):
    """
    Returns the field dependency for the given name.
    """
    if not name:
        return None

    doc = frappe.get_doc("HD Form Script", name)

    res = frappe._dict()
    res["name"] = doc.name
    res["parent_field"] = doc.name.split("-")[1]
    res["child_field"] = doc.name.split("-")[2]
    res["enabled"] = doc.enabled
    res["parent_child_mapping"] = frappe.parse_json(doc.script.split("//JSON: ")[-1])
    res["fields_criteria"] = get_fields_criteria(doc.script)

    return res


def get_fields_criteria(script):
    match = re.search(r"//FieldsCriteria: (.+)", script)
    if not match:
        return None
    fields_criteria_str = match.group(1)
    return frappe.parse_json(fields_criteria_str)


@frappe.whitelist()
def create_update_field_dependency(
    parent_field, child_field, parent_child_mapping, enabled, fields_criteria
):
    frappe.has_permission("HD Form Script", "create", throw=True)
    if not parent_field or not child_field or not parent_child_mapping:
        frappe.throw(
            _("Parent field, child field, and parent-child mapping are required.")
        )

    script_doc = get_or_create_standard_form_script(parent_field, child_field)
    script_doc.enabled = enabled
    script_doc.apply_on_new_page = 1

    func = generate_on_change_function(
        parent_child_mapping=frappe.parse_json(parent_child_mapping),
        parent_field=parent_field,
        child_field=child_field,
    )
    script = add_function_to_script(
        parent_field,
        child_field,
        func,
    )
    # add JSON for UI
    script += "\n"
    script += "// This JSON is to render the field dependency in the UI.\n"
    script += "//FieldsCriteria: " + frappe.as_json(fields_criteria) + "\n"
    script += "//JSON: " + frappe.as_json(parent_child_mapping) + "\n"

    old_fields_criteria = get_fields_criteria(script_doc.script)

    script_doc.script = script
    script_doc.save()

    handle_fields_criteria(
        parent_field, child_field, fields_criteria, old_fields_criteria
    )
    # To avoid the message "HD Ticket updated" from showing up
    # frappe.local.message_log = []


def get_or_create_standard_form_script(parent_field, child_field):
    if existing_doc := frappe.db.exists(
        "HD Form Script",
        {"name": ["like", f"Field Dependency-{parent_field}-{child_field}"]},
    ):
        return frappe.get_doc("HD Form Script", existing_doc)
    else:
        doc = frappe.new_doc("HD Form Script")
        doc.is_standard = 1
        doc.name = f"Field Dependency-{parent_field}-{child_field}"
        return doc


def generate_on_change_function(parent_child_mapping, parent_field, child_field):
    script = f"function update_{child_field}(value){{\n"
    first = True
    for parent, children in parent_child_mapping.items():
        options = ",".join([f'"{child}"' for child in children])
        if first:
            script += f'        if(value=="{parent}") {{\n'
            first = False
        else:
            script += f'        else if(value=="{parent}") {{\n'

        script += f"            options = [{options}]\n"
        script += f'            applyFilters("{child_field}",options)\n'
        script += "        }\n"
        script += "\n"
    script += "        else {\n"
    script += f'            applyFilters("{child_field}",[])\n'
    script += "        }\n"

    script += "    }\n"
    script += "\n"
    return script


def add_function_to_script(parent_field, child_field, func):

    script = "// This script is auto-generated by the Field Dependency feature. \n"
    script += "// It is not meant to be modified directly. \n"
    script += "\n"

    script += "function setupForm({doc, updateField, call, router, toast, $dialog, createToast ,applyFilters}) {"
    script += "\n"
    script += "\n"
    script += f"    {func}"
    script += "\n"
    script += f"""   return {{
        onChange: {{
            {parent_field}: (newVal) => update_{child_field}(newVal)
        }}
    }}
    """
    script += "\n"
    script += "}"
    return script


def handle_fields_criteria(
    parent_field, child_field, fields_criteria, old_fields_criteria
):

    if frappe.as_json(fields_criteria) == frappe.as_json(old_fields_criteria):
        return

    fields_criteria = frappe.parse_json(fields_criteria)
    display_depends_on = fields_criteria.get("display", "")
    mandatory_depends_on = fields_criteria.get("mandatory", False)

    if not display_depends_on and not mandatory_depends_on:
        return

    display_expression = get_df_expression(
        parent_field, child_field, display_depends_on
    )
    mandatory_expression = get_df_expression(
        parent_field, child_field, mandatory_depends_on
    )

    handle_form_customization(child_field, display_expression, mandatory_expression)


def get_df_expression(parent_field, child_field, criteria):
    if not criteria.get("enabled", False):
        return None
    values = criteria.get("value", [])

    values = [v.get("value") for v in values]
    if len(values) == 0:
        return None
    expression = ""
    if values[0] == "Any":
        expression = f"eval:doc.{parent_field} != ''"
    else:
        expression = f"eval:{values}.includes(doc.{parent_field})"

    return expression


def handle_form_customization(field, display_expression, mandatory_expression):
    cf = frappe.get_doc("Customize Form")
    cf.doc_type = "HD Ticket"
    cf.fetch_to_customize()
    for f in cf.fields:
        if f.fieldname == field:
            f.depends_on = display_expression
            f.mandatory_depends_on = mandatory_expression

    cf.save_customization()
    frappe.clear_cache("HD Ticket")
