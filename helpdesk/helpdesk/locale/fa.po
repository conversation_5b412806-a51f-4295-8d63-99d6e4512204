msgid ""
msgstr ""
"Project-Id-Version: frappe\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2025-09-07 10:34+0000\n"
"PO-Revision-Date: 2025-09-15 18:50\n"
"Last-Translator: <EMAIL>\n"
"Language-Team: Persian\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.16.0\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: frappe\n"
"X-Crowdin-Project-ID: 639578\n"
"X-Crowdin-Language: fa\n"
"X-Crowdin-File: /[frappe.helpdesk] develop/helpdesk/locale/main.pot\n"
"X-Crowdin-File-ID: 118\n"
"Language: fa_IR\n"

#: helpdesk/helpdesk/doctype/hd_ticket_status/hd_ticket_status.py:114
msgid "- Default Ticket Status<br>"
msgstr ""

#: helpdesk/helpdesk/doctype/hd_ticket_status/hd_ticket_status.py:116
msgid "- Ticket Reopen Status<br>"
msgstr ""

#: helpdesk/helpdesk/doctype/hd_ticket_status/hd_ticket_status.py:92
msgid "- {0} as Default Status<br>"
msgstr ""

#: helpdesk/helpdesk/doctype/hd_ticket_status/hd_ticket_status.py:94
msgid "- {0} as Reopen Status<br>"
msgstr ""

#. Description of the 'Feedback' (Select) field in DocType 'HD Article
#. Feedback'
#: helpdesk/helpdesk/doctype/hd_article_feedback/hd_article_feedback.json
msgid "0 is no feedback, 1 is Like, 2 is Dislike"
msgstr ""

#: helpdesk/helpdesk/doctype/hd_ticket_status/hd_ticket_status.py:117
msgid "<br>Please update HD Settings to use a different status before disabling this status."
msgstr ""

#: helpdesk/helpdesk/doctype/hd_ticket_status/hd_ticket_status.py:95
msgid "<br>Please update the SLA(s) to use a different status before disabling this status."
msgstr ""

#. Header text in the Helpdesk Workspace
#: helpdesk/helpdesk/workspace/helpdesk/helpdesk.json
msgid "<span class=\"h3\" style=\"\"><a href=\"/helpdesk\">Visit Helpdesk</a></span>"
msgstr ""

#. Paragraph text in the Helpdesk Workspace
#: helpdesk/helpdesk/workspace/helpdesk/helpdesk.json
msgid "<span class=\"h4\">Helpdesk Configuration<br></span>"
msgstr ""

#. Option for the 'Source Type' (Select) field in DocType 'HD Support Search
#. Source'
#. Label of the api_sb (Section Break) field in DocType 'HD Support Search
#. Source'
#: helpdesk/helpdesk/doctype/hd_support_search_source/hd_support_search_source.json
msgid "API"
msgstr "API"

#: helpdesk/api/knowledge_base.py:15
msgid "Access denied"
msgstr ""

#. Label of the acknowledgement_section (Section Break) field in DocType 'HD
#. Settings'
#: desk/src/components/Settings/EmailNotifications/NotificationList.vue:72
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "Acknowledgement"
msgstr ""

#. Label of the action (Data) field in DocType 'HD Ticket Activity'
#: helpdesk/helpdesk/doctype/hd_ticket_activity/hd_ticket_activity.json
msgid "Action"
msgstr "اقدام"

#: helpdesk/helpdesk/doctype/hd_ticket_priority/hd_ticket_priority.py:22
msgid "Action Required"
msgstr ""

#. Description of the 'On click (Javascript)' (Code) field in DocType 'HD
#. Action'
#: helpdesk/helpdesk/doctype/hd_action/hd_action.json
msgid "Action to perform when clicking the button. The doc will be available as `this`"
msgstr ""

#: desk/src/components/Settings/Agents.vue:139
#: desk/src/components/Settings/Assignment Rules/AssigneeSearch.vue:97
msgid "Add Agents"
msgstr ""

#: desk/src/components/Settings/Assignment Rules/AssigneeSearch.vue:9
msgid "Add Assignee"
msgstr ""

#. Label of the add_weekly_holidays (Section Break) field in DocType 'HD
#. Service Holiday List'
#: helpdesk/helpdesk/doctype/hd_service_holiday_list/hd_service_holiday_list.json
msgid "Add Weekly Holidays"
msgstr "افزودن تعطیلات هفتگی"

#: desk/src/components/Settings/Assignment Rules/AssignmentRulesSection.vue:17
msgid "Add a condition"
msgstr ""

#: desk/src/components/Settings/Sla/SlaAssignmentConditions.vue:14
msgid "Add a custom condition"
msgstr ""

#: desk/src/components/Settings/Assignment Rules/AssignmentRulesSection.vue:29
#: desk/src/components/Settings/Sla/SlaAssignmentConditions.vue:25
msgid "Add condition"
msgstr ""

#. Label of the get_weekly_off_dates (Button) field in DocType 'HD Service
#. Holiday List'
#: helpdesk/helpdesk/doctype/hd_service_holiday_list/hd_service_holiday_list.json
msgid "Add to Holidays"
msgstr "افزودن به تعطیلات"

#: desk/src/components/Settings/Agents.vue:6
msgid "Add, manage agents and assign roles to them."
msgstr ""

#. Label of the about (Code) field in DocType 'HD Ticket Template'
#: helpdesk/helpdesk/doctype/hd_ticket_template/hd_ticket_template.json
msgid "Additional Information"
msgstr "اطلاعات تکمیلی"

#. Name of a role
#: helpdesk/helpdesk/doctype/hd_customer/hd_customer.json
#: helpdesk/helpdesk/doctype/hd_team/hd_team.json
#: helpdesk/helpdesk/doctype/hd_view/hd_view.json
msgid "Administrator"
msgstr "مدیر"

#. Name of a role
#. Label of the to_agent (Link) field in DocType 'HD Escalation Rule'
#. Label of a Link in the Helpdesk Workspace
#: helpdesk/helpdesk/doctype/hd_action/hd_action.json
#: helpdesk/helpdesk/doctype/hd_agent/hd_agent.json
#: helpdesk/helpdesk/doctype/hd_article/hd_article.json
#: helpdesk/helpdesk/doctype/hd_article_category/hd_article_category.json
#: helpdesk/helpdesk/doctype/hd_canned_response/hd_canned_response.json
#: helpdesk/helpdesk/doctype/hd_customer/hd_customer.json
#: helpdesk/helpdesk/doctype/hd_escalation_rule/hd_escalation_rule.json
#: helpdesk/helpdesk/doctype/hd_notification/hd_notification.json
#: helpdesk/helpdesk/doctype/hd_service_holiday_list/hd_service_holiday_list.json
#: helpdesk/helpdesk/doctype/hd_service_level_agreement/hd_service_level_agreement.json
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
#: helpdesk/helpdesk/doctype/hd_team/hd_team.json
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
#: helpdesk/helpdesk/doctype/hd_ticket_activity/hd_ticket_activity.json
#: helpdesk/helpdesk/doctype/hd_ticket_comment/hd_ticket_comment.json
#: helpdesk/helpdesk/doctype/hd_ticket_feedback_option/hd_ticket_feedback_option.json
#: helpdesk/helpdesk/doctype/hd_ticket_priority/hd_ticket_priority.json
#: helpdesk/helpdesk/doctype/hd_ticket_template/hd_ticket_template.json
#: helpdesk/helpdesk/doctype/hd_ticket_type/hd_ticket_type.json
#: helpdesk/helpdesk/doctype/hd_view/hd_view.json
#: helpdesk/helpdesk/workspace/helpdesk/helpdesk.json
msgid "Agent"
msgstr "عامل"

#. Label of a Card Break in the Helpdesk Workspace
#: helpdesk/helpdesk/workspace/helpdesk/helpdesk.json
msgid "Agent Configuration"
msgstr ""

#. Name of a role
#: helpdesk/helpdesk/doctype/hd_form_script/hd_form_script.json
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
#: helpdesk/helpdesk/doctype/hd_ticket_status/hd_ticket_status.json
#: helpdesk/helpdesk/doctype/hd_view/hd_view.json
msgid "Agent Manager"
msgstr ""

#. Label of the agent_name (Data) field in DocType 'HD Agent'
#: helpdesk/helpdesk/doctype/hd_agent/hd_agent.json
msgid "Agent Name"
msgstr ""

#: desk/src/components/Settings/Agents.vue:5
msgid "Agents"
msgstr "عوامل"

#. Name of a role
#: helpdesk/helpdesk/doctype/hd_agent/hd_agent.json
#: helpdesk/helpdesk/doctype/hd_article/hd_article.json
#: helpdesk/helpdesk/doctype/hd_article_category/hd_article_category.json
#: helpdesk/helpdesk/doctype/hd_article_feedback/hd_article_feedback.json
#: helpdesk/helpdesk/doctype/hd_form_script/hd_form_script.json
#: helpdesk/helpdesk/doctype/hd_service_level_agreement/hd_service_level_agreement.json
#: helpdesk/helpdesk/doctype/hd_team/hd_team.json
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
#: helpdesk/helpdesk/doctype/hd_ticket_feedback_option/hd_ticket_feedback_option.json
#: helpdesk/helpdesk/doctype/hd_ticket_priority/hd_ticket_priority.json
#: helpdesk/helpdesk/doctype/hd_ticket_status/hd_ticket_status.json
#: helpdesk/helpdesk/doctype/hd_ticket_template/hd_ticket_template.json
#: helpdesk/helpdesk/doctype/hd_ticket_type/hd_ticket_type.json
msgid "All"
msgstr "همه"

#. Label of the allow_anyone_to_create_tickets (Check) field in DocType 'HD
#. Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "Allow anyone to create tickets"
msgstr ""

#. Option for the 'Color' (Select) field in DocType 'HD Ticket Status'
#: helpdesk/helpdesk/doctype/hd_ticket_status/hd_ticket_status.json
msgid "Amber"
msgstr "کهربایی"

#. Label of the apply_sla_for_resolution (Check) field in DocType 'HD Service
#. Level Agreement'
#: helpdesk/helpdesk/doctype/hd_service_level_agreement/hd_service_level_agreement.json
msgid "Apply SLA for Resolution Time"
msgstr "اعمال SLA برای زمان حل و فصل"

#. Label of the apply_to (Select) field in DocType 'HD Form Script'
#: helpdesk/helpdesk/doctype/hd_form_script/hd_form_script.json
msgid "Apply To"
msgstr "درخواست به"

#. Label of the apply_on_new_page (Check) field in DocType 'HD Form Script'
#: helpdesk/helpdesk/doctype/hd_form_script/hd_form_script.json
msgid "Apply on new page"
msgstr ""

#. Label of the apply_to_customer_portal (Check) field in DocType 'HD Form
#. Script'
#: helpdesk/helpdesk/doctype/hd_form_script/hd_form_script.json
msgid "Apply to customer portal"
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'HD Article'
#: helpdesk/helpdesk/doctype/hd_article/hd_article.json
msgid "Archived"
msgstr "بایگانی شد"

#: desk/src/components/Settings/SettingsModal.vue:44
msgid "Are you sure you want to change tabs? Unsaved changes will be lost."
msgstr ""

#: desk/src/components/Settings/EmailNotifications/Notification.vue:114
msgid "Are you sure you want to go back? Unsaved changes will be lost."
msgstr ""

#: desk/src/components/Settings/EmailNotifications/Notification.vue:127
msgid "Are you sure you want to reset the content? Current content will be overridden."
msgstr ""

#. Label of the article (Link) field in DocType 'HD Article Feedback'
#: helpdesk/helpdesk/doctype/hd_article_feedback/hd_article_feedback.json
msgid "Article"
msgstr ""

#. Label of the assign_to_section (Section Break) field in DocType 'HD
#. Escalation Rule'
#: helpdesk/helpdesk/doctype/hd_escalation_rule/hd_escalation_rule.json
msgid "Assign to"
msgstr ""

#: helpdesk/helpdesk/report/ticket_analytics/ticket_analytics.js:62
#: helpdesk/helpdesk/report/ticket_summary/ticket_summary.js:49
msgid "Assigned To"
msgstr "اختصاص یافته به"

#: desk/src/components/Settings/Assignment Rules/AssigneeRules.vue:5
msgid "Assignee Rules"
msgstr ""

#: desk/src/components/Settings/Assignment Rules/AssigneeRules.vue:75
msgid "Assignees"
msgstr ""

#. Option for the 'Type' (Select) field in DocType 'HD Notification'
#: helpdesk/helpdesk/doctype/hd_notification/hd_notification.json
msgid "Assignment"
msgstr "تخصیص"

#. Label of the filters_section (Section Break) field in DocType 'HD Service
#. Level Agreement'
#: helpdesk/helpdesk/doctype/hd_service_level_agreement/hd_service_level_agreement.json
msgid "Assignment Conditions"
msgstr "شرایط تخصیص"

#. Label of the assignment_rule (Link) field in DocType 'HD Team'
#. Label of a Link in the Helpdesk Workspace
#: helpdesk/helpdesk/doctype/hd_team/hd_team.json
#: helpdesk/helpdesk/workspace/helpdesk/helpdesk.json
msgid "Assignment Rule"
msgstr "قانون تخصیص"

#. Label of the assignment_rules_section (Section Break) field in DocType 'HD
#. Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "Assignment Rules"
msgstr "قوانین تخصیص"

#: desk/src/components/Settings/Assignment Rules/AssignmentRulesList.vue:10
msgid "Assignment Rules automatically route tickets to the right team members based on predefined conditions."
msgstr ""

#: desk/src/components/Settings/Assignment Rules/AssignmentRuleView.vue:278
msgid "Assignment Schedule"
msgstr ""

#: desk/src/components/Settings/Assignment Rules/AssignmentRuleView.vue:134
msgid "Assignment condition"
msgstr ""

#: desk/src/components/Settings/Assignment Rules/AssignmentRulesListView.vue:19
msgid "Assignment rule"
msgstr ""

#: desk/src/components/Settings/Assignment Rules/AssignmentRulesList.vue:6
msgid "Assignment rules"
msgstr ""

#: helpdesk/helpdesk/doctype/hd_ticket_status/hd_ticket_status.py:73
msgid "At least one enabled status for <u>{0}</u> category must be enabled."
msgstr ""

#: helpdesk/helpdesk/doctype/hd_escalation_rule/hd_escalation_rule.py:28
msgid "At-least one of priority, team and ticket type is required"
msgstr ""

#. Label of the attachment (Attach) field in DocType 'HD Ticket'
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
msgid "Attachment"
msgstr "پیوست"

#. Label of the author (Link) field in DocType 'HD Article'
#: helpdesk/helpdesk/doctype/hd_article/hd_article.json
msgid "Author"
msgstr "نویسنده"

#. Label of the auto_update_status (Check) field in DocType 'HD Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "Auto update status"
msgstr ""

#. Label of the auto_close_after_days (Int) field in DocType 'HD Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "Auto-close after (Days)"
msgstr ""

#. Description of the 'Condition' (Code) field in DocType 'HD Service Level
#. Agreement'
#: helpdesk/helpdesk/doctype/hd_service_level_agreement/hd_service_level_agreement.json
msgid "Auto-generated from the portal view — do not edit directly unless you know what you're doing! "
msgstr ""

#. Label of the auto_close_tickets (Check) field in DocType 'HD Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "Automatically Close Tickets when"
msgstr ""

#. Label of a Card Break in the Helpdesk Workspace
#: helpdesk/helpdesk/workspace/helpdesk/helpdesk.json
msgid "Automation"
msgstr "اتوماسیون"

#. Label of the avg_response_time (Duration) field in DocType 'HD Ticket'
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
msgid "Average Response Time"
msgstr "میانگین زمان پاسخگویی"

#. Label of the base_support_rotation (Link) field in DocType 'HD Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "Base Support Rotation"
msgstr ""

#. Label of the base_url (Data) field in DocType 'HD Support Search Source'
#: helpdesk/helpdesk/doctype/hd_support_search_source/hd_support_search_source.json
msgid "Base URL"
msgstr "URL پایه"

#: helpdesk/helpdesk/report/ticket_analytics/ticket_analytics.js:9
#: helpdesk/helpdesk/report/ticket_summary/ticket_summary.js:9
msgid "Based On"
msgstr "بر اساس"

#: helpdesk/helpdesk/doctype/hd_service_holiday_list/hd_service_holiday_list.js:34
msgid "Based on your HR Policy, select your leave allocation period's end date"
msgstr "بر اساس سیاست منابع انسانی خود، تاریخ پایان دوره تخصیص مرخصی خود را انتخاب کنید"

#: helpdesk/helpdesk/doctype/hd_service_holiday_list/hd_service_holiday_list.js:27
msgid "Based on your HR Policy, select your leave allocation period's start date"
msgstr "بر اساس سیاست منابع انسانی خود، تاریخ شروع دوره تخصیص مرخصی خود را انتخاب کنید"

#. Option for the 'Color' (Select) field in DocType 'HD Ticket Status'
#: helpdesk/helpdesk/doctype/hd_ticket_status/hd_ticket_status.json
msgid "Black"
msgstr "مشکی"

#. Option for the 'Color' (Select) field in DocType 'HD Ticket Status'
#: helpdesk/helpdesk/doctype/hd_ticket_status/hd_ticket_status.json
msgid "Blue"
msgstr "آبی"

#. Label of the branding_tab (Tab Break) field in DocType 'HD Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "Branding"
msgstr ""

#. Label of the button_section (Section Break) field in DocType 'HD Action'
#: helpdesk/helpdesk/doctype/hd_action/hd_action.json
msgid "Button"
msgstr "دکمه"

#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.py:596
msgid "Can not send email. No sender email set up!"
msgstr ""

#: helpdesk/helpdesk/doctype/hd_service_level_agreement/hd_service_level_agreement.py:500
msgid "Cannot delete the default SLA. At least one SLA must be marked as default."
msgstr ""

#: helpdesk/helpdesk/doctype/hd_ticket_status/hd_ticket_status.py:110
msgid "Cannot disable this status as it is linked in HD Settings:<br><br>"
msgstr ""

#: helpdesk/helpdesk/doctype/hd_ticket_status/hd_ticket_status.py:88
msgid "Cannot disable this status as it is linked in the following SLAs:<br><br>"
msgstr ""

#: helpdesk/api/knowledge_base.py:122
msgid "Cannot merge General category"
msgstr ""

#. Label of the category (Link) field in DocType 'HD Article'
#. Label of the category (Select) field in DocType 'HD Ticket Status'
#: helpdesk/helpdesk/doctype/hd_article/hd_article.json
#: helpdesk/helpdesk/doctype/hd_ticket_status/hd_ticket_status.json
msgid "Category"
msgstr "دسته بندی"

#: helpdesk/api/knowledge_base.py:72
#: helpdesk/helpdesk/doctype/hd_article/hd_article.py:59
msgid "Category must have atleast one article"
msgstr ""

#. Label of a Card Break in the Helpdesk Workspace
#: helpdesk/helpdesk/workspace/helpdesk/helpdesk.json
msgid "Channels"
msgstr ""

#: desk/src/components/Settings/Assignment Rules/AssigneeRules.vue:22
msgid "Choose how tickets are distributed among selected assignees."
msgstr ""

#: desk/src/components/Settings/Assignment Rules/AssignmentRuleView.vue:282
msgid "Choose the days of the week when this rule should be active."
msgstr ""

#: desk/src/components/Settings/Assignment Rules/AssignmentRuleView.vue:139
msgid "Choose which tickets are affected by this assignment rule."
msgstr ""

#: desk/src/components/Settings/Assignment Rules/AssignmentRuleView.vue:209
msgid "Choose which tickets are affected by this un-assignment rule."
msgstr ""

#: desk/src/components/Settings/Assignment Rules/AssigneeRules.vue:78
msgid "Choose who receives the tickets."
msgstr ""

#. Label of the clear_table (Button) field in DocType 'HD Service Holiday List'
#: helpdesk/helpdesk/doctype/hd_service_holiday_list/hd_service_holiday_list.json
msgid "Clear Table"
msgstr "پاک کردن جدول"

#: helpdesk/helpdesk/doctype/hd_service_holiday_list/hd_service_holiday_list.js:46
msgid "Click on Add to Holidays. This will populate the holidays table with all the dates that fall on the selected weekly off. Repeat the process for populating the dates for all your weekly holidays"
msgstr "روی افزودن به تعطیلات کلیک کنید. با این کار جدول تعطیلات با تمام تاریخ‌هایی که در تعطیلات هفتگی انتخاب شده قرار می گیرند پر می‌کند. فرآیند پر کردن تاریخ‌ها را برای تمام تعطیلات هفتگی خود تکرار کنید"

#: desk/src/components/Settings/Assignment Rules/AssignmentRuleListItem.vue:61
msgid "Close"
msgstr "بستن"

msgid "Closed or rated tickets cannot be updated by non-agents"
msgstr ""

#. Label of the color (Color) field in DocType 'HD Service Holiday List'
#. Label of the color (Select) field in DocType 'HD Ticket Status'
#: helpdesk/helpdesk/doctype/hd_service_holiday_list/hd_service_holiday_list.json
#: helpdesk/helpdesk/doctype/hd_ticket_status/hd_ticket_status.json
msgid "Color"
msgstr "رنگ"

#. Label of the columns (Code) field in DocType 'HD View'
#: helpdesk/helpdesk/doctype/hd_view/hd_view.json
msgid "Columns"
msgstr "ستون ها"

#. Label of the reference_comment (Link) field in DocType 'HD Notification'
#: helpdesk/helpdesk/doctype/hd_notification/hd_notification.json
msgid "Comment"
msgstr "دیدگاه"

#. Label of the commented_by (Link) field in DocType 'HD Ticket Comment'
#: helpdesk/helpdesk/doctype/hd_ticket_comment/hd_ticket_comment.json
msgid "Commented By"
msgstr ""

#. Label of the condition (Code) field in DocType 'HD Service Level Agreement'
#. Label of the condition_json (Code) field in DocType 'HD Service Level
#. Agreement'
#: helpdesk/helpdesk/doctype/hd_service_level_agreement/hd_service_level_agreement.json
msgid "Condition"
msgstr "شرط"

#. Label of the contact (Link) field in DocType 'HD Organization Contact Item'
#. Label of the contact (Link) field in DocType 'HD Ticket'
#: helpdesk/helpdesk/doctype/hd_organization_contact_item/hd_organization_contact_item.json
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
#: helpdesk/helpdesk/report/ticket_analytics/ticket_analytics.js:56
#: helpdesk/helpdesk/report/ticket_analytics/ticket_analytics.py:44
#: helpdesk/helpdesk/report/ticket_summary/ticket_summary.js:43
#: helpdesk/helpdesk/report/ticket_summary/ticket_summary.py:72
msgid "Contact"
msgstr "مخاطب"

#. Label of the contact_html (HTML) field in DocType 'HD Customer'
#: helpdesk/helpdesk/doctype/hd_customer/hd_customer.json
msgid "Contact HTML"
msgstr "با HTML تماس بگیرید"

#: helpdesk/templates/components/contact_with_us.html:11
msgid "Contact with us"
msgstr ""

#. Label of the contacts (Table) field in DocType 'HD Organization'
#: helpdesk/helpdesk/doctype/hd_organization/hd_organization.json
msgid "Contacts"
msgstr "مخاطب"

#. Label of the content_section (Section Break) field in DocType 'HD Article'
#. Label of the content (Text Editor) field in DocType 'HD Article'
#. Label of the content (Text Editor) field in DocType 'HD Ticket Comment'
#: helpdesk/helpdesk/doctype/hd_article/hd_article.json
#: helpdesk/helpdesk/doctype/hd_ticket_comment/hd_ticket_comment.json
msgid "Content"
msgstr "محتوا"

#. Label of the content_type (Data) field in DocType 'HD Ticket'
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
msgid "Content Type"
msgstr "نوع محتوا"

#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.py:625
msgid "Could not an email due to: {0}"
msgstr ""

#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.py:785
msgid "Could not send an acknowledgement email due to: {0}"
msgstr ""

#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.py:626
msgid "Could not an email due to: {0}"
msgstr ""

#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.py:786
msgid "Could not send an acknowledgement email due to: {0}"
msgstr ""

msgid "Could not send feedback email,due to: {0}"
msgstr ""

#: desk/src/components/Settings/Assignment Rules/AssignmentRulesList.vue:17
msgid "Create new"
msgstr ""

#. Label of the criterion_section (Section Break) field in DocType 'HD
#. Escalation Rule'
#: helpdesk/helpdesk/doctype/hd_escalation_rule/hd_escalation_rule.json
msgid "Criterion"
msgstr ""

#. Label of the customer (Link) field in DocType 'HD Ticket'
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
msgid "Customer"
msgstr "مشتری"

#. Label of the customer_name (Data) field in DocType 'HD Customer'
#: helpdesk/helpdesk/doctype/hd_customer/hd_customer.json
msgid "Customer Name"
msgstr "نام مشتری"

#: desk/src/components/Settings/EmailNotifications/NotificationList.vue:7
msgid "Customize your email notification preferences to stay informed about important updates and activities."
msgstr ""

#. Option for the 'Color' (Select) field in DocType 'HD Ticket Status'
#: helpdesk/helpdesk/doctype/hd_ticket_status/hd_ticket_status.json
msgid "Cyan"
msgstr "فیروزه ای"

#. Label of the holiday_date (Date) field in DocType 'HD Holiday'
#: helpdesk/helpdesk/doctype/hd_holiday/hd_holiday.json
#: helpdesk/helpdesk/report/support_hour_distribution/support_hour_distribution.py:77
msgid "Date"
msgstr "تاریخ"

#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.py:29
msgid "Day count for auto closing tickets cannot be negative or zero"
msgstr ""

#. Label of the default_priority (Link) field in DocType 'HD Service Level
#. Agreement'
#. Label of the default_priority (Check) field in DocType 'HD Service Level
#. Priority'
#: helpdesk/helpdesk/doctype/hd_service_level_agreement/hd_service_level_agreement.json
#: helpdesk/helpdesk/doctype/hd_service_level_priority/hd_service_level_priority.json
msgid "Default Priority"
msgstr "اولویت پیش‌فرض"

#. Label of the default_sla (Check) field in DocType 'HD Service Level
#. Agreement'
#: helpdesk/helpdesk/doctype/hd_service_level_agreement/hd_service_level_agreement.json
msgid "Default SLA"
msgstr ""

#. Label of the default_ticket_status (Link) field in DocType 'HD Service Level
#. Agreement'
#: helpdesk/helpdesk/doctype/hd_service_level_agreement/hd_service_level_agreement.json
msgid "Default Ticket Status"
msgstr "وضعیت پیش‌فرض تیکت"

#. Label of the default_priority (Link) field in DocType 'HD Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "Default priority"
msgstr ""

#: helpdesk/helpdesk/doctype/hd_ticket_template/hd_ticket_template.py:59
msgid "Default template can not be deleted"
msgstr ""

#. Label of the default_ticket_status (Link) field in DocType 'HD Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "Default ticket status"
msgstr "وضعیت پیش‌فرض تیکت"

#. Label of the default_ticket_type (Link) field in DocType 'HD Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "Default ticket type"
msgstr ""

#: desk/src/components/Settings/Assignment Rules/AssigneeRules.vue:9
msgid "Define who receives the tickets and how they’re distributed among agents."
msgstr ""

#. Label of the description (Text) field in DocType 'HD Article Category'
#. Label of the description (Text Editor) field in DocType 'HD Holiday'
#. Label of the description (Data) field in DocType 'HD Service Holiday List'
#. Label of the description (Data) field in DocType 'HD Service Level
#. Agreement'
#. Label of the description (Text Editor) field in DocType 'HD Ticket'
#. Label of the description (Small Text) field in DocType 'HD Ticket Priority'
#. Label of the description (Small Text) field in DocType 'HD Ticket Type'
#: helpdesk/helpdesk/doctype/hd_article_category/hd_article_category.json
#: helpdesk/helpdesk/doctype/hd_holiday/hd_holiday.json
#: helpdesk/helpdesk/doctype/hd_service_holiday_list/hd_service_holiday_list.json
#: helpdesk/helpdesk/doctype/hd_service_level_agreement/hd_service_level_agreement.json
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
#: helpdesk/helpdesk/doctype/hd_ticket_priority/hd_ticket_priority.json
#: helpdesk/helpdesk/doctype/hd_ticket_type/hd_ticket_type.json
msgid "Description"
msgstr "شرح"

#. Label of the description_template (Text Editor) field in DocType 'HD Ticket
#. Template'
#: helpdesk/helpdesk/doctype/hd_ticket_template/hd_ticket_template.json
msgid "Description Template"
msgstr ""

#. Label of the description_weight (Int) field in DocType 'HD Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "Description Weight"
msgstr ""

#. Label of the details_section (Section Break) field in DocType 'HD
#. Notification'
#. Label of the sb_details (Section Break) field in DocType 'HD Ticket'
#: helpdesk/helpdesk/doctype/hd_notification/hd_notification.json
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
msgid "Details"
msgstr "جزئیات"

#. Description of the 'Ignore Restrictions' (Check) field in DocType 'HD Team'
#: helpdesk/helpdesk/doctype/hd_team/hd_team.json
msgid "Do not apply filters and restrictions to agents of this team"
msgstr ""

#. Label of the do_not_restrict_tickets_without_an_agent_group (Check) field in
#. DocType 'HD Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "Do not restrict tickets without a Team"
msgstr ""

#. Label of the dt (Link) field in DocType 'HD Form Script'
#. Label of the dt (Link) field in DocType 'HD View'
#: helpdesk/helpdesk/doctype/hd_form_script/hd_form_script.json
#: helpdesk/helpdesk/doctype/hd_view/hd_view.json
msgid "DocType"
msgstr "DocType"

#. Label of the domain (Data) field in DocType 'HD Customer'
#: helpdesk/helpdesk/doctype/hd_customer/hd_customer.json
msgid "Domain"
msgstr "دامنه"

#. Option for the 'Status' (Select) field in DocType 'HD Article'
#: helpdesk/helpdesk/doctype/hd_article/hd_article.json
msgid "Draft"
msgstr "پیش‌نویس"

#: desk/src/components/Settings/Assignment Rules/AssignmentRuleListItem.vue:64
msgid "Duplicate"
msgstr "تکرار کردن"

#: desk/src/components/Settings/Assignment Rules/AssignmentRuleListItem.vue:45
msgid "Duplicate Assignment Rule"
msgstr ""

#. Label of the email (Data) field in DocType 'HD Desk Account Request'
#: helpdesk/helpdesk/doctype/hd_desk_account_request/hd_desk_account_request.json
msgid "Email"
msgstr "ایمیل"

#. Label of the email_account (Link) field in DocType 'HD Ticket'
#. Label of a Link in the Helpdesk Workspace
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
#: helpdesk/helpdesk/workspace/helpdesk/helpdesk.json
msgid "Email Account"
msgstr "حساب کاربری ایمیل"

#. Label of the feedback_email_content (Text) field in DocType 'HD Settings'
#. Label of the acknowledgement_email_content (Text) field in DocType 'HD
#. Settings'
#. Label of the reply_email_to_agent_content (Text) field in DocType 'HD
#. Settings'
#. Label of the reply_via_agent_email_content (Text) field in DocType 'HD
#. Settings'
#: desk/src/components/Settings/EmailNotifications/Notification.vue:74
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "Email Content"
msgstr ""

#. Label of the email_customisations_tab (Tab Break) field in DocType 'HD
#. Settings'
#: desk/src/components/Settings/EmailNotifications/NotificationList.vue:5
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "Email Notifications"
msgstr ""

#. Label of the is_feedback_mandatory (Check) field in DocType 'HD Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "Enable feedback for Customer Portal"
msgstr ""

#. Label of the is_enabled (Check) field in DocType 'HD Action'
#. Label of the is_enabled (Check) field in DocType 'HD Escalation Rule'
#. Label of the enabled (Check) field in DocType 'HD Form Script'
#. Label of the enabled (Check) field in DocType 'HD Service Level Agreement'
#. Label of the enable_email_ticket_feedback (Check) field in DocType 'HD
#. Settings'
#. Label of the send_acknowledgement_email (Check) field in DocType 'HD
#. Settings'
#. Label of the enable_reply_email_to_agent (Check) field in DocType 'HD
#. Settings'
#. Label of the enable_reply_email_via_agent (Check) field in DocType 'HD
#. Settings'
#. Label of the enabled (Check) field in DocType 'HD Stopword'
#. Label of the enabled (Check) field in DocType 'HD Ticket Status'
#: desk/src/components/Settings/Assignment Rules/AssignmentRuleView.vue:38
#: desk/src/components/Settings/Assignment Rules/AssignmentRulesListView.vue:21
#: desk/src/components/Settings/EmailNotifications/Notification.vue:42
#: helpdesk/helpdesk/doctype/hd_action/hd_action.json
#: helpdesk/helpdesk/doctype/hd_escalation_rule/hd_escalation_rule.json
#: helpdesk/helpdesk/doctype/hd_form_script/hd_form_script.json
#: helpdesk/helpdesk/doctype/hd_service_level_agreement/hd_service_level_agreement.json
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
#: helpdesk/helpdesk/doctype/hd_stopword/hd_stopword.json
#: helpdesk/helpdesk/doctype/hd_ticket_status/hd_ticket_status.json
msgid "Enabled"
msgstr "فعال"

#. Label of the end_date (Date) field in DocType 'HD Service Level Agreement'
#: helpdesk/helpdesk/doctype/hd_service_level_agreement/hd_service_level_agreement.json
msgid "End Date"
msgstr "تاریخ پایان"

#. Label of the end_time (Time) field in DocType 'HD Service Day'
#: helpdesk/helpdesk/doctype/hd_service_day/hd_service_day.json
msgid "End Time"
msgstr "زمان پایان"

#: helpdesk/helpdesk/doctype/hd_service_holiday_list/hd_service_holiday_list.js:22
msgid "Enter a name for this HD Service Holiday List."
msgstr ""

#: helpdesk/api/knowledge_base.py:80
msgid "Error moving article to category"
msgstr ""

#: helpdesk/overrides/email_account.py:69
msgid "Error while connecting to email account {0}"
msgstr "خطا هنگام اتصال به حساب ایمیل {0}"

#: helpdesk/helpdesk/doctype/hd_escalation_rule/hd_escalation_rule.py:43
msgid "Escalation rule already exists for this criteria"
msgstr ""

#. Label of the is_external_link (Check) field in DocType 'HD Action'
#: helpdesk/helpdesk/doctype/hd_action/hd_action.json
msgid "External link"
msgstr ""

#. Option for the 'SLA Status' (Select) field in DocType 'HD Ticket'
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
msgid "Failed"
msgstr "ناموفق"

#. Label of the feedback (Select) field in DocType 'HD Article Feedback'
#. Label of the feedback_section (Section Break) field in DocType 'HD Settings'
#. Label of the feedback_tab (Tab Break) field in DocType 'HD Ticket'
#: helpdesk/helpdesk/doctype/hd_article_feedback/hd_article_feedback.json
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
msgid "Feedback"
msgstr "بازخورد"

#. Label of the feedback_extra (Small Text) field in DocType 'HD Email
#. Feedback'
#: helpdesk/helpdesk/doctype/hd_email_feedback/hd_email_feedback.json
msgid "Feedback "
msgstr "بازخورد "

#. Label of the feedback_extra (Long Text) field in DocType 'HD Ticket'
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
msgid "Feedback (Extra)"
msgstr ""

#. Label of the feedback (Link) field in DocType 'HD Ticket'
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
msgid "Feedback (Option)"
msgstr ""

#: helpdesk/helpdesk/doctype/hd_email_feedback/hd_email_feedback.py:30
msgid "Feedback Already Exists"
msgstr ""

#. Label of the feedback_rating (Rating) field in DocType 'HD Email Feedback'
#: helpdesk/helpdesk/doctype/hd_email_feedback/hd_email_feedback.json
msgid "Feedback Rating"
msgstr ""

#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.py:160
msgid "Feedback email has been sent to the customer"
msgstr ""

#. Label of the fieldname (Data) field in DocType 'HD Ticket Template Field'
#: helpdesk/helpdesk/doctype/hd_ticket_template_field/hd_ticket_template_field.json
msgid "Field (fieldname)"
msgstr ""

#: helpdesk/helpdesk/doctype/hd_ticket_template/hd_ticket_template.py:27
msgid "Field `{0}` does not exist in Ticket"
msgstr ""

#: helpdesk/helpdesk/doctype/hd_ticket_template/hd_ticket_template.py:43
msgid "Field `{0}` is not allowed in Ticket Template"
msgstr ""

#. Label of the fields (Table) field in DocType 'HD Ticket Template'
#: helpdesk/helpdesk/doctype/hd_ticket_template/hd_ticket_template.json
msgid "Fields"
msgstr "فیلدها"

#. Label of the filters_tab (Tab Break) field in DocType 'HD View'
#. Label of the filters (Code) field in DocType 'HD View'
#: helpdesk/helpdesk/doctype/hd_view/hd_view.json
msgid "Filters"
msgstr "فیلترها"

#: desk/src/components/Settings/EmailNotifications/Notification.vue:83
msgid "Find out all of the variables that can be used in the content"
msgstr ""

#. Label of the first_responded_on (Datetime) field in DocType 'HD Ticket'
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
msgid "First Responded On"
msgstr "اولین پاسخ در"

#. Option for the 'SLA Status' (Select) field in DocType 'HD Ticket'
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
msgid "First Response Due"
msgstr "اولین پاسخ به علت"

#. Label of the response_time (Duration) field in DocType 'HD Service Level
#. Priority'
#. Label of the first_response_time (Duration) field in DocType 'HD Ticket'
#: helpdesk/helpdesk/doctype/hd_service_level_priority/hd_service_level_priority.json
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
msgid "First Response Time"
msgstr "اولین زمان پاسخگویی"

#. Name of a report
#: helpdesk/helpdesk/report/first_response_time_for_tickets/first_response_time_for_tickets.json
msgid "First Response Time for Tickets"
msgstr ""

#. Option for the 'Apply To' (Select) field in DocType 'HD Form Script'
#: helpdesk/helpdesk/doctype/hd_form_script/hd_form_script.json
msgid "Form"
msgstr "فرم"

#. Option for the 'Workday' (Select) field in DocType 'HD Service Day'
#. Option for the 'Weekly Off' (Select) field in DocType 'HD Service Holiday
#. List'
#: helpdesk/helpdesk/doctype/hd_service_day/hd_service_day.json
#: helpdesk/helpdesk/doctype/hd_service_holiday_list/hd_service_holiday_list.json
msgid "Friday"
msgstr "جمعه"

#. Label of the user_from (Link) field in DocType 'HD Notification'
#: helpdesk/helpdesk/doctype/hd_notification/hd_notification.json
msgid "From"
msgstr "از"

#. Label of the from_date (Date) field in DocType 'HD Service Holiday List'
#: helpdesk/helpdesk/doctype/hd_service_holiday_list/hd_service_holiday_list.json
#: helpdesk/helpdesk/report/first_response_time_for_tickets/first_response_time_for_tickets.js:9
#: helpdesk/helpdesk/report/support_hour_distribution/support_hour_distribution.js:8
#: helpdesk/helpdesk/report/ticket_analytics/ticket_analytics.js:17
#: helpdesk/helpdesk/report/ticket_summary/ticket_summary.js:17
msgid "From Date"
msgstr "از تاریخ"

#. Option for the 'SLA Status' (Select) field in DocType 'HD Ticket'
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
msgid "Fulfilled"
msgstr "برآورده شد"

#. Label of a Link in the Helpdesk Workspace
#: helpdesk/helpdesk/workspace/helpdesk/helpdesk.json
msgid "General Settings"
msgstr "تنظیمات عمومی"

#: helpdesk/helpdesk/doctype/hd_article_category/hd_article_category.py:24
msgid "General category can't be deleted"
msgstr ""

#: helpdesk/helpdesk/doctype/hd_article_category/hd_article_category.py:20
msgid "General category name can't be changed"
msgstr ""

#. Option for the 'Color' (Select) field in DocType 'HD Ticket Status'
#: helpdesk/helpdesk/doctype/hd_ticket_status/hd_ticket_status.json
msgid "Gray"
msgstr "خاکستری"

#. Option for the 'Color' (Select) field in DocType 'HD Ticket Status'
#: helpdesk/helpdesk/doctype/hd_ticket_status/hd_ticket_status.json
msgid "Green"
msgstr "سبز"

#. Label of the group_by_tab (Tab Break) field in DocType 'HD View'
#: helpdesk/helpdesk/doctype/hd_view/hd_view.json
msgid "Group By"
msgstr "دسته‌بندی بر اساس"

#. Label of the group_by_field (Data) field in DocType 'HD View'
#: helpdesk/helpdesk/doctype/hd_view/hd_view.json
msgid "Group By Field"
msgstr ""

#. Name of a role
#: helpdesk/helpdesk/doctype/hd_article/hd_article.json
#: helpdesk/helpdesk/doctype/hd_article_category/hd_article_category.json
#: helpdesk/helpdesk/doctype/hd_view/hd_view.json
msgid "Guest"
msgstr "مهمان"

#. Name of a DocType
#: helpdesk/helpdesk/doctype/hd_action/hd_action.json
msgid "HD Action"
msgstr ""

#. Name of a DocType
#: helpdesk/helpdesk/doctype/hd_agent/hd_agent.json
msgid "HD Agent"
msgstr ""

#. Name of a DocType
#: helpdesk/helpdesk/doctype/hd_article/hd_article.json
msgid "HD Article"
msgstr ""

#. Name of a DocType
#: helpdesk/helpdesk/doctype/hd_article_category/hd_article_category.json
msgid "HD Article Category"
msgstr ""

#. Name of a DocType
#: helpdesk/helpdesk/doctype/hd_article_feedback/hd_article_feedback.json
msgid "HD Article Feedback"
msgstr ""

#. Name of a DocType
#: helpdesk/helpdesk/doctype/hd_canned_response/hd_canned_response.json
msgid "HD Canned Response"
msgstr ""

#. Name of a DocType
#: helpdesk/helpdesk/doctype/hd_customer/hd_customer.json
msgid "HD Customer"
msgstr ""

#. Name of a DocType
#: helpdesk/helpdesk/doctype/hd_desk_account_request/hd_desk_account_request.json
msgid "HD Desk Account Request"
msgstr ""

#. Name of a DocType
#: helpdesk/helpdesk/doctype/hd_email_feedback/hd_email_feedback.json
msgid "HD Email Feedback"
msgstr ""

#. Name of a DocType
#: helpdesk/helpdesk/doctype/hd_escalation_rule/hd_escalation_rule.json
msgid "HD Escalation Rule"
msgstr ""

#. Name of a DocType
#: helpdesk/helpdesk/doctype/hd_form_script/hd_form_script.json
msgid "HD Form Script"
msgstr ""

#. Name of a DocType
#: helpdesk/helpdesk/doctype/hd_holiday/hd_holiday.json
msgid "HD Holiday"
msgstr ""

#. Name of a DocType
#: helpdesk/helpdesk/doctype/hd_notification/hd_notification.json
msgid "HD Notification"
msgstr ""

#. Name of a DocType
#: helpdesk/helpdesk/doctype/hd_organization/hd_organization.json
msgid "HD Organization"
msgstr ""

#. Name of a DocType
#: helpdesk/helpdesk/doctype/hd_organization_contact_item/hd_organization_contact_item.json
msgid "HD Organization Contact Item"
msgstr ""

#. Name of a DocType
#: helpdesk/helpdesk/doctype/hd_portal_signup_request/hd_portal_signup_request.json
msgid "HD Portal Signup Request"
msgstr ""

#. Name of a DocType
#: helpdesk/helpdesk/doctype/hd_service_day/hd_service_day.json
msgid "HD Service Day"
msgstr ""

#. Name of a DocType
#: helpdesk/helpdesk/doctype/hd_service_holiday_list/hd_service_holiday_list.json
#: helpdesk/helpdesk/doctype/hd_service_holiday_list/hd_service_holiday_list_calendar.js:20
msgid "HD Service Holiday List"
msgstr ""

#. Name of a DocType
#: helpdesk/helpdesk/doctype/hd_service_level_agreement/hd_service_level_agreement.json
msgid "HD Service Level Agreement"
msgstr ""

#. Name of a DocType
#: helpdesk/helpdesk/doctype/hd_service_level_priority/hd_service_level_priority.json
msgid "HD Service Level Priority"
msgstr ""

#. Name of a DocType
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "HD Settings"
msgstr ""

#. Name of a DocType
#: helpdesk/helpdesk/doctype/hd_stopword/hd_stopword.json
msgid "HD Stopword"
msgstr ""

#. Name of a DocType
#: helpdesk/helpdesk/doctype/hd_support_search_source/hd_support_search_source.json
msgid "HD Support Search Source"
msgstr ""

#. Name of a DocType
#: helpdesk/helpdesk/doctype/hd_synonym/hd_synonym.json
msgid "HD Synonym"
msgstr ""

#. Name of a DocType
#: helpdesk/helpdesk/doctype/hd_synonyms/hd_synonyms.json
msgid "HD Synonyms"
msgstr ""

#. Name of a DocType
#: helpdesk/helpdesk/doctype/hd_team/hd_team.json
msgid "HD Team"
msgstr ""

#. Name of a DocType
#: helpdesk/helpdesk/doctype/hd_team_member/hd_team_member.json
msgid "HD Team Member"
msgstr ""

#. Name of a DocType
#. Label of the ticket (Link) field in DocType 'HD Ticket Activity'
#: helpdesk/helpdesk/doctype/hd_service_level_agreement/hd_service_level_agreement_dashboard.py:7
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
#: helpdesk/helpdesk/doctype/hd_ticket_activity/hd_ticket_activity.json
msgid "HD Ticket"
msgstr ""

#. Name of a DocType
#: helpdesk/helpdesk/doctype/hd_ticket_activity/hd_ticket_activity.json
msgid "HD Ticket Activity"
msgstr ""

#. Name of a DocType
#: helpdesk/helpdesk/doctype/hd_ticket_comment/hd_ticket_comment.json
msgid "HD Ticket Comment"
msgstr ""

#. Name of a DocType
#: helpdesk/helpdesk/doctype/hd_ticket_feedback_option/hd_ticket_feedback_option.json
msgid "HD Ticket Feedback Option"
msgstr ""

#. Name of a DocType
#: helpdesk/helpdesk/doctype/hd_ticket_priority/hd_ticket_priority.json
msgid "HD Ticket Priority"
msgstr ""

#. Name of a DocType
#: helpdesk/helpdesk/doctype/hd_ticket_status/hd_ticket_status.json
msgid "HD Ticket Status"
msgstr ""

#. Name of a DocType
#: helpdesk/helpdesk/doctype/hd_ticket_template/hd_ticket_template.json
msgid "HD Ticket Template"
msgstr ""

#. Name of a DocType
#: helpdesk/helpdesk/doctype/hd_ticket_template_field/hd_ticket_template_field.json
msgid "HD Ticket Template Field"
msgstr ""

#. Name of a DocType
#: helpdesk/helpdesk/doctype/hd_ticket_type/hd_ticket_type.json
msgid "HD Ticket Type"
msgstr ""

#. Name of a DocType
#: helpdesk/helpdesk/doctype/hd_view/hd_view.json
msgid "HD View"
msgstr ""

#. Label of the headings_weight (Int) field in DocType 'HD Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "Headings Weight"
msgstr ""

#: helpdesk/config/desktop.py:11
msgid "HelpDesk"
msgstr ""

#. Name of a Workspace
#: helpdesk/helpdesk/workspace/helpdesk/helpdesk.json
msgid "Helpdesk"
msgstr ""

#. Name of a role
#: helpdesk/helpdesk/doctype/hd_ticket_priority/hd_ticket_priority.json
#: helpdesk/helpdesk/doctype/hd_ticket_type/hd_ticket_type.json
msgid "Helpdesk Contact"
msgstr ""

#. Label of a Card Break in the Helpdesk Workspace
#: helpdesk/helpdesk/workspace/helpdesk/helpdesk.json
msgid "Helpdesk Reports"
msgstr ""

#: helpdesk/helpdesk/doctype/hd_service_holiday_list/hd_service_holiday_list.js:53
msgid "Here, your weekly offs are pre-populated based on the previous selections. You can add more rows to also add public and national holidays individually."
msgstr "در اینجا، تخفیف‌های هفتگی شما بر اساس انتخاب‌های قبلی از قبل پر شده است. می‌توانید ردیف‌های بیشتری اضافه کنید تا تعطیلات عمومی و ملی را به‌صورت جداگانه اضافه کنید."

#. Label of the cond_hidden (Code) field in DocType 'HD Action'
#: helpdesk/helpdesk/doctype/hd_action/hd_action.json
msgid "Hide condition"
msgstr ""

#. Label of the hide_from_customer (Check) field in DocType 'HD Ticket Template
#. Field'
#: helpdesk/helpdesk/doctype/hd_ticket_template_field/hd_ticket_template_field.json
msgid "Hide from customer"
msgstr ""

#. Label of the holiday_list (Link) field in DocType 'HD Service Level
#. Agreement'
#: helpdesk/helpdesk/doctype/hd_service_level_agreement/hd_service_level_agreement.json
msgid "Holiday List"
msgstr "لیست تعطیلات"

#. Label of the holiday_list_name (Data) field in DocType 'HD Service Holiday
#. List'
#: helpdesk/helpdesk/doctype/hd_service_holiday_list/hd_service_holiday_list.json
msgid "Holiday List Name"
msgstr "نام لیست تعطیلات"

#. Label of the holidays_section (Section Break) field in DocType 'HD Service
#. Holiday List'
#. Label of the holidays (Table) field in DocType 'HD Service Holiday List'
#: helpdesk/helpdesk/doctype/hd_service_holiday_list/hd_service_holiday_list.json
msgid "Holidays"
msgstr "تعطیلات"

#. Label of the ip_address (Data) field in DocType 'HD Desk Account Request'
#: helpdesk/helpdesk/doctype/hd_desk_account_request/hd_desk_account_request.json
msgid "IP Address"
msgstr "آدرس IP"

#. Label of the button_icon (Data) field in DocType 'HD Action'
#. Label of the icon (Data) field in DocType 'HD Article Category'
#. Label of the icon (Data) field in DocType 'HD View'
#: helpdesk/helpdesk/doctype/hd_action/hd_action.json
#: helpdesk/helpdesk/doctype/hd_article_category/hd_article_category.json
#: helpdesk/helpdesk/doctype/hd_view/hd_view.json
msgid "Icon"
msgstr "آیکون"

#. Description of the 'Enabled' (Check) field in DocType 'HD Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "If enabled, a ticket creation acknowledgement email will be sent to the user right after creating an email ticket"
msgstr ""

#. Description of the 'Enabled' (Check) field in DocType 'HD Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "If enabled, an email is sent to all of the recipients associated with an agent's reply"
msgstr ""

#. Description of the 'Enabled' (Check) field in DocType 'HD Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "If enabled, an email will be sent to all of the assigned agents after a reply from one of the contacts"
msgstr ""

#. Description of the 'Allow anyone to create tickets' (Check) field in DocType
#. 'HD Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "If enabled, anyone will be able to create tickets (without any permission). "
msgstr ""

#. Description of the 'Enabled' (Check) field in DocType 'HD Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "If enabled, feedback email will be sent to the customer when you mark a ticket as the status selected below.\n"
msgstr ""

#. Description of the 'Enable feedback for Customer Portal' (Check) field in
#. DocType 'HD Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "If enabled, the feedback dialog will be shown, when a user tries to close a ticket. \n"
msgstr ""

#. Label of the ignore_restrictions (Check) field in DocType 'HD Team'
#: helpdesk/helpdesk/doctype/hd_team/hd_team.json
msgid "Ignore Restrictions"
msgstr ""

#. Label of the user_image (Data) field in DocType 'HD Agent'
#. Label of the image (Attach Image) field in DocType 'HD Customer'
#: helpdesk/helpdesk/doctype/hd_agent/hd_agent.json
#: helpdesk/helpdesk/doctype/hd_customer/hd_customer.json
msgid "Image"
msgstr "تصویر"

#. Description of the 'Logo' (Attach Image) field in DocType 'HD Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "Image to be used in various places, including Login and Signup pages. An image with transparent background and 160 x 32 is preferred"
msgstr ""

#. Label of the images_column (Column Break) field in DocType 'HD Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "Images"
msgstr "تصاویر"

#. Label of the instantly_send_email (Check) field in DocType 'HD Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "Instantly send e-mail"
msgstr ""

#: helpdesk/utils.py:26
msgid "Insufficient Permission for {0}"
msgstr "مجوز ناکافی برای {0}"

#. Label of the integer_value (Int) field in DocType 'HD Ticket Priority'
#: helpdesk/helpdesk/doctype/hd_ticket_priority/hd_ticket_priority.json
msgid "Integer value"
msgstr ""

#: helpdesk/api/settings/email_notifications.py:112
msgid "Invalid notification"
msgstr ""

#: desk/src/components/Settings/Assignment Rules/AssigneeSearch.vue:82
msgid "Invite agent"
msgstr ""

#. Label of the is_active (Check) field in DocType 'HD Agent'
#: helpdesk/helpdesk/doctype/hd_agent/hd_agent.json
msgid "Is Active"
msgstr "فعال است"

#. Label of the is_customer_portal (Check) field in DocType 'HD View'
#: helpdesk/helpdesk/doctype/hd_view/hd_view.json
msgid "Is Customer Portal"
msgstr ""

#. Label of the is_default (Check) field in DocType 'HD View'
#: helpdesk/helpdesk/doctype/hd_view/hd_view.json
msgid "Is Default"
msgstr "پیش‌فرض است"

#. Label of the is_standard (Check) field in DocType 'HD Form Script'
#: helpdesk/helpdesk/doctype/hd_form_script/hd_form_script.json
msgid "Is Standard"
msgstr "استاندارد است"

#. Label of the initial_helpdesk_name_setup_skipped (Check) field in DocType
#. 'HD Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "Is name setup skipped"
msgstr ""

#. Label of the is_pinned (Check) field in DocType 'HD Ticket Comment'
#: helpdesk/helpdesk/doctype/hd_ticket_comment/hd_ticket_comment.json
msgid "Is pinned"
msgstr ""

#. Label of the setup_complete (Check) field in DocType 'HD Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "Is setup complete"
msgstr ""

#. Label of the key (Data) field in DocType 'HD Email Feedback'
#. Label of the key (Data) field in DocType 'HD Ticket'
#: helpdesk/helpdesk/doctype/hd_email_feedback/hd_email_feedback.json
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
msgid "Key"
msgstr "کلید"

#. Label of the knowledge_base_section (Section Break) field in DocType 'HD
#. Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "Knowledge Base"
msgstr "دانش محور"

#. Label of the button_label (Data) field in DocType 'HD Action'
#. Label of the label (Data) field in DocType 'HD Ticket Feedback Option'
#. Label of the label_agent (Data) field in DocType 'HD Ticket Status'
#. Label of the label (Data) field in DocType 'HD View'
#: helpdesk/helpdesk/doctype/hd_action/hd_action.json
#: helpdesk/helpdesk/doctype/hd_ticket_feedback_option/hd_ticket_feedback_option.json
#: helpdesk/helpdesk/doctype/hd_ticket_status/hd_ticket_status.json
#: helpdesk/helpdesk/doctype/hd_view/hd_view.json
msgid "Label"
msgstr "برچسب"

#. Label of the label_customer (Data) field in DocType 'HD Ticket Status'
#: helpdesk/helpdesk/doctype/hd_ticket_status/hd_ticket_status.json
msgid "Label (customer view)"
msgstr ""

#: desk/src/components/Settings/Assignment Rules/AssigneeRules.vue:102
msgid "Last"
msgstr ""

#: desk/src/components/Settings/Assignment Rules/AssigneeRules.vue:95
msgid "Last user assigned by this rule"
msgstr ""

#: desk/src/components/Settings/Assignment Rules/AssignmentRuleView.vue:145
#: desk/src/components/Settings/Assignment Rules/AssignmentRuleView.vue:217
msgid "Learn about conditions"
msgstr ""

#. Option for the 'Source Type' (Select) field in DocType 'HD Support Search
#. Source'
#: helpdesk/helpdesk/doctype/hd_support_search_source/hd_support_search_source.json
msgid "Link"
msgstr ""

#. Label of the link_options_sb (Section Break) field in DocType 'HD Support
#. Search Source'
#: helpdesk/helpdesk/doctype/hd_support_search_source/hd_support_search_source.json
msgid "Link Options"
msgstr "گزینه‌های پیوند"

#. Option for the 'Apply To' (Select) field in DocType 'HD Form Script'
#. Label of the list_tab (Tab Break) field in DocType 'HD View'
#: helpdesk/helpdesk/doctype/hd_form_script/hd_form_script.json
#: helpdesk/helpdesk/doctype/hd_view/hd_view.json
msgid "List"
msgstr "لیست"

#. Label of the load_default_columns (Check) field in DocType 'HD View'
#: helpdesk/helpdesk/doctype/hd_view/hd_view.json
msgid "Load Default Columns"
msgstr ""

#. Label of the brand_logo (Attach Image) field in DocType 'HD Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "Logo"
msgstr "لوگو"

#. Description of the 'Integer value' (Int) field in DocType 'HD Ticket
#. Priority'
#: helpdesk/helpdesk/doctype/hd_ticket_priority/hd_ticket_priority.json
msgid "Lower the Integer value, higher is the priority of the issue"
msgstr ""

#. Option for the 'Type' (Select) field in DocType 'HD Notification'
#: helpdesk/helpdesk/doctype/hd_notification/hd_notification.json
msgid "Mention"
msgstr "اشاره"

#. Label of the merged_with (Link) field in DocType 'HD Ticket'
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
msgid "Merged With"
msgstr ""

#. Label of the message (Text Editor) field in DocType 'HD Canned Response'
#. Label of the message (Text) field in DocType 'HD Notification'
#: helpdesk/helpdesk/doctype/hd_canned_response/hd_canned_response.json
#: helpdesk/helpdesk/doctype/hd_notification/hd_notification.json
msgid "Message"
msgstr "پیام"

#. Label of the meta_tab (Tab Break) field in DocType 'HD Ticket'
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
msgid "Meta"
msgstr "متا"

#. Label of the misc_tab (Tab Break) field in DocType 'HD Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "Misc"
msgstr ""

#. Option for the 'Workday' (Select) field in DocType 'HD Service Day'
#. Option for the 'Weekly Off' (Select) field in DocType 'HD Service Holiday
#. List'
#: helpdesk/helpdesk/doctype/hd_service_day/hd_service_day.json
#: helpdesk/helpdesk/doctype/hd_service_holiday_list/hd_service_holiday_list.json
msgid "Monday"
msgstr "دوشنبه"

#: helpdesk/helpdesk/report/ticket_analytics/ticket_analytics.js:35
msgid "Monthly"
msgstr "ماهانه"

#. Label of the category_name (Data) field in DocType 'HD Article Category'
#. Label of the organization_name (Data) field in DocType 'HD Organization'
#. Label of the team_name (Data) field in DocType 'HD Team'
#: helpdesk/helpdesk/doctype/hd_article_category/hd_article_category.json
#: helpdesk/helpdesk/doctype/hd_organization/hd_organization.json
#: helpdesk/helpdesk/doctype/hd_team/hd_team.json
msgid "Name"
msgstr "نام"

#. Label of the name_weight (Int) field in DocType 'HD Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "Name Weight"
msgstr ""

#. Description of the 'Label (customer view)' (Data) field in DocType 'HD
#. Ticket Status'
#: helpdesk/helpdesk/doctype/hd_ticket_status/hd_ticket_status.json
msgid "Name shown on the customer portal<br> (e.g., Replied → Awaiting Response)."
msgstr ""

#: desk/src/components/Settings/Assignment Rules/AssignmentRuleListItem.vue:51
msgid "New Assignment Rule Name"
msgstr ""

#: desk/src/components/Settings/Assignment Rules/AssignmentRulesListView.vue:14
msgid "No items in the list"
msgstr ""

#: desk/src/components/Settings/Assignment Rules/AssigneeSearch.vue:74
msgid "No results found"
msgstr "نتیجه‌ای یافت نشد"

#: helpdesk/utils.py:159
msgid "Not Allowed"
msgstr "مجاز نیست"

#: helpdesk/helpdesk/report/ticket_analytics/ticket_analytics.py:250
#: helpdesk/helpdesk/report/ticket_summary/ticket_summary.py:270
#: helpdesk/helpdesk/report/ticket_summary/ticket_summary.py:363
msgid "Not Specified"
msgstr "مشخص نشده است"

#. Label of a Link in the Helpdesk Workspace
#: helpdesk/helpdesk/workspace/helpdesk/helpdesk.json
msgid "Notification Settings"
msgstr "تنظیمات اعلان"

#: desk/src/components/Settings/Assignment Rules/AssignmentRuleView.vue:154
#: desk/src/components/Settings/Assignment Rules/AssignmentRuleView.vue:232
msgid "Old Condition"
msgstr ""

#. Label of the on_hold_since (Datetime) field in DocType 'HD Ticket'
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
msgid "On Hold Since"
msgstr "در انتظار از آنجایی که"

#: desk/src/components/Settings/EmailNotifications/ShareFeedback.vue:20
msgid "On Ticket Status"
msgstr ""

#. Label of the action (Code) field in DocType 'HD Action'
#: helpdesk/helpdesk/doctype/hd_action/hd_action.json
msgid "On click (Javascript)"
msgstr ""

#: helpdesk/helpdesk/doctype/hd_view/hd_view.py:29
msgid "Only one default view is allowed per user for {0}"
msgstr ""

#: helpdesk/helpdesk/doctype/hd_ticket_status/hd_ticket_status.py:29
msgid "Only the 'color' and 'order' fields of the 'Closed' status can be modified."
msgstr ""

#. Option for the 'Category' (Select) field in DocType 'HD Ticket Status'
#: helpdesk/helpdesk/doctype/hd_ticket_status/hd_ticket_status.json
msgid "Open"
msgstr "باز"

#. Description of the 'Category' (Select) field in DocType 'HD Ticket Status'
#: helpdesk/helpdesk/doctype/hd_ticket_status/hd_ticket_status.json
msgid "Open: SLA continues. <br>\n"
"Paused: SLA is paused. <br>\n"
"Resolved: SLA timer stops."
msgstr ""

#. Label of the opening_date (Date) field in DocType 'HD Ticket'
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
msgid "Opening Date"
msgstr "تاریخ افتتاحیه"

#. Label of the opening_time (Time) field in DocType 'HD Ticket'
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
msgid "Opening Time"
msgstr "زمان بازگشایی"

#. Option for the 'Color' (Select) field in DocType 'HD Ticket Status'
#: helpdesk/helpdesk/doctype/hd_ticket_status/hd_ticket_status.json
msgid "Orange"
msgstr "نارنجی"

#. Label of the order (Int) field in DocType 'HD Ticket Status'
#: helpdesk/helpdesk/doctype/hd_ticket_status/hd_ticket_status.json
msgid "Order"
msgstr "سفارش"

#. Label of the order_by_tab (Tab Break) field in DocType 'HD View'
#. Label of the order_by (Code) field in DocType 'HD View'
#: helpdesk/helpdesk/doctype/hd_view/hd_view.json
msgid "Order By"
msgstr "سفارش توسط"

#: helpdesk/api/settings/field_dependency.py:59
msgid "Parent field, child field, and parent-child mapping are required."
msgstr ""

#. Option for the 'SLA Status' (Select) field in DocType 'HD Ticket'
#. Option for the 'Category' (Select) field in DocType 'HD Ticket Status'
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
#: helpdesk/helpdesk/doctype/hd_ticket_status/hd_ticket_status.json
msgid "Paused"
msgstr "مکث کرد"

#. Option for the 'Color' (Select) field in DocType 'HD Ticket Status'
#: helpdesk/helpdesk/doctype/hd_ticket_status/hd_ticket_status.json
msgid "Pink"
msgstr "صورتی"

#. Label of the pinned (Check) field in DocType 'HD View'
#: helpdesk/helpdesk/doctype/hd_view/hd_view.json
msgid "Pinned"
msgstr ""

#. Label of the placeholder (Data) field in DocType 'HD Ticket Template Field'
#: helpdesk/helpdesk/doctype/hd_ticket_template_field/hd_ticket_template_field.json
msgid "Placeholder"
msgstr "جای‌نگهدار"

#: helpdesk/helpdesk/doctype/hd_ticket_priority/hd_ticket_priority.py:19
msgid "Please add this priority in the <a href='/app/hd-service-level-agreement'>required SLA documents</a>"
msgstr ""

#: helpdesk/helpdesk/doctype/hd_service_level_agreement/hd_service_level_agreement.py:318
msgid "Please add {0} priority in {1} SLA"
msgstr ""

#: helpdesk/helpdesk/doctype/hd_service_holiday_list/hd_service_holiday_list.py:41
msgid "Please select weekly off day"
msgstr "لطفاً روز تعطیل هفتگی را انتخاب کنید"

#. Label of the post_description_key (Data) field in DocType 'HD Support Search
#. Source'
#: helpdesk/helpdesk/doctype/hd_support_search_source/hd_support_search_source.json
msgid "Post Description Key"
msgstr "کلید توضیحات پست"

#. Label of the post_route_key_list (Data) field in DocType 'HD Support Search
#. Source'
#: helpdesk/helpdesk/doctype/hd_support_search_source/hd_support_search_source.json
msgid "Post Route Key List"
msgstr "فهرست کلید مسیر ارسال"

#. Label of the post_route (Data) field in DocType 'HD Support Search Source'
#: helpdesk/helpdesk/doctype/hd_support_search_source/hd_support_search_source.json
msgid "Post Route String"
msgstr "رشته مسیر ارسال"

#. Label of the post_title_key (Data) field in DocType 'HD Support Search
#. Source'
#: helpdesk/helpdesk/doctype/hd_support_search_source/hd_support_search_source.json
msgid "Post Title Key"
msgstr "کلید عنوان پست"

#. Label of the prefer_knowledge_base (Check) field in DocType 'HD Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "Prefer knowledge base"
msgstr ""

#. Label of the priorities (Table) field in DocType 'HD Service Level
#. Agreement'
#: helpdesk/helpdesk/doctype/hd_service_level_agreement/hd_service_level_agreement.json
msgid "Priorities"
msgstr "اولویت های"

#. Label of the priority (Link) field in DocType 'HD Escalation Rule'
#. Label of the to_priority (Link) field in DocType 'HD Escalation Rule'
#. Label of the priority (Link) field in DocType 'HD Service Level Priority'
#. Label of the priority_section (Section Break) field in DocType 'HD Settings'
#. Label of the priority (Link) field in DocType 'HD Ticket'
#. Label of the priority (Link) field in DocType 'HD Ticket Type'
#: desk/src/components/Settings/Assignment Rules/AssignmentRulesListView.vue:20
#: helpdesk/helpdesk/doctype/hd_escalation_rule/hd_escalation_rule.json
#: helpdesk/helpdesk/doctype/hd_service_level_priority/hd_service_level_priority.json
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
#: helpdesk/helpdesk/doctype/hd_ticket_type/hd_ticket_type.json
msgid "Priority"
msgstr "اولویت"

#: helpdesk/helpdesk/doctype/hd_service_level_agreement/hd_service_level_agreement.py:84
msgid "Priority <u>{0}</u> must be included in the SLA {1}."
msgstr ""

#: helpdesk/helpdesk/doctype/hd_service_level_agreement/hd_service_level_agreement.py:75
msgid "Priority {0} has been repeated."
msgstr "اولویت {0} تکرار شده است."

#. Label of the public (Check) field in DocType 'HD View'
#: helpdesk/helpdesk/doctype/hd_view/hd_view.json
msgid "Public"
msgstr "عمومی"

#. Option for the 'Status' (Select) field in DocType 'HD Article'
#: helpdesk/helpdesk/doctype/hd_article/hd_article.json
msgid "Published"
msgstr "منتشر شده"

#. Label of the published_on (Datetime) field in DocType 'HD Article'
#: helpdesk/helpdesk/doctype/hd_article/hd_article.json
msgid "Published On"
msgstr "منتشر شده در"

#: helpdesk/helpdesk/report/ticket_analytics/ticket_analytics.js:36
msgid "Quarterly"
msgstr "سه ماه یکبار"

#. Label of the query_options_sb (Section Break) field in DocType 'HD Support
#. Search Source'
#: helpdesk/helpdesk/doctype/hd_support_search_source/hd_support_search_source.json
msgid "Query Options"
msgstr "گزینه‌های پرسمان"

#. Label of the query_route (Data) field in DocType 'HD Support Search Source'
#: helpdesk/helpdesk/doctype/hd_support_search_source/hd_support_search_source.json
msgid "Query Route String"
msgstr "رشته مسیر پرسمان"

#. Label of the raised_by (Data) field in DocType 'HD Ticket'
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
msgid "Raised By (Email)"
msgstr "مطرح شده توسط (ایمیل)"

#: helpdesk/helpdesk/report/ticket_analytics/ticket_analytics.js:31
msgid "Range"
msgstr "دامنه"

#. Label of the feedback_rating (Rating) field in DocType 'HD Ticket'
#. Label of the rating (Rating) field in DocType 'HD Ticket Feedback Option'
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
#: helpdesk/helpdesk/doctype/hd_ticket_feedback_option/hd_ticket_feedback_option.json
msgid "Rating"
msgstr "رتبه بندی"

#: helpdesk/helpdesk/doctype/hd_ticket_feedback_option/hd_ticket_feedback_option.py:22
msgid "Rating must be between 0.2 and 1.0"
msgstr ""

#: helpdesk/helpdesk/doctype/hd_ticket_feedback_option/hd_ticket_feedback_option.py:18
msgid "Rating {0} is not allowed"
msgstr ""

#. Option for the 'Type' (Select) field in DocType 'HD Notification'
#: helpdesk/helpdesk/doctype/hd_notification/hd_notification.json
msgid "Reaction"
msgstr ""

#. Label of the read (Check) field in DocType 'HD Notification'
#: helpdesk/helpdesk/doctype/hd_notification/hd_notification.json
msgid "Read"
msgstr "خواندن"

#. Label of the recurring_holidays (JSON) field in DocType 'HD Service Holiday
#. List'
#: helpdesk/helpdesk/doctype/hd_service_holiday_list/hd_service_holiday_list.json
msgid "Recurring holidays"
msgstr ""

#. Option for the 'Color' (Select) field in DocType 'HD Ticket Status'
#: helpdesk/helpdesk/doctype/hd_ticket_status/hd_ticket_status.json
msgid "Red"
msgstr "قرمز"

#. Label of the reference_tab (Tab Break) field in DocType 'HD Ticket'
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
msgid "Reference"
msgstr "ارجاع"

#. Label of the reference_ticket (Link) field in DocType 'HD Ticket Comment'
#: helpdesk/helpdesk/doctype/hd_ticket_comment/hd_ticket_comment.json
msgid "Reference Ticket"
msgstr ""

#. Label of the references_tab (Tab Break) field in DocType 'HD Customer'
#. Label of the references_section (Section Break) field in DocType 'HD
#. Notification'
#: helpdesk/helpdesk/doctype/hd_customer/hd_customer.json
#: helpdesk/helpdesk/doctype/hd_notification/hd_notification.json
msgid "References"
msgstr "منابع"

#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.js:6
msgid "Regenerate Search Index"
msgstr ""

#. Label of the reply_from_agent_section (Section Break) field in DocType 'HD
#. Settings'
#: desk/src/components/Settings/EmailNotifications/NotificationList.vue:84
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "Reply From Agent"
msgstr ""

#. Label of the reply_from_contact_section (Section Break) field in DocType 'HD
#. Settings'
#: desk/src/components/Settings/EmailNotifications/NotificationList.vue:77
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "Reply From Contact"
msgstr ""

#. Label of the request_key (Data) field in DocType 'HD Desk Account Request'
#. Label of the request_key (Data) field in DocType 'HD Portal Signup Request'
#: helpdesk/helpdesk/doctype/hd_desk_account_request/hd_desk_account_request.json
#: helpdesk/helpdesk/doctype/hd_portal_signup_request/hd_portal_signup_request.json
msgid "Request Key"
msgstr ""

#. Label of the required (Check) field in DocType 'HD Ticket Template Field'
#: helpdesk/helpdesk/doctype/hd_ticket_template_field/hd_ticket_template_field.json
msgid "Required"
msgstr ""

#: desk/src/components/Settings/EmailNotifications/Notification.vue:102
msgid "Reset Content"
msgstr ""

#: desk/src/components/Settings/EmailNotifications/Notification.vue:125
msgid "Reset content"
msgstr ""

#. Label of the resolution_tab (Tab Break) field in DocType 'HD Ticket'
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
msgid "Resolution"
msgstr "حل و فصل"

#. Label of the resolution_by (Datetime) field in DocType 'HD Ticket'
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
msgid "Resolution By"
msgstr "حل و فصل توسط"

#. Label of the resolution_date (Datetime) field in DocType 'HD Ticket'
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
msgid "Resolution Date"
msgstr "تاریخ حل و فصل"

#. Label of the resolution_details (Text Editor) field in DocType 'HD Ticket'
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
msgid "Resolution Details"
msgstr "جزئیات حل و فصل"

#. Option for the 'SLA Status' (Select) field in DocType 'HD Ticket'
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
msgid "Resolution Due"
msgstr "سررسید حل و فصل"

#. Label of the resolution_time (Duration) field in DocType 'HD Service Level
#. Priority'
#. Label of the resolution_time (Duration) field in DocType 'HD Ticket'
#: helpdesk/helpdesk/doctype/hd_service_level_priority/hd_service_level_priority.json
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
msgid "Resolution Time"
msgstr "مدت زمان حل و فصل"

#. Option for the 'Category' (Select) field in DocType 'HD Ticket Status'
#: helpdesk/helpdesk/doctype/hd_ticket_status/hd_ticket_status.json
msgid "Resolved"
msgstr "حل شد"

#. Label of the response_tab (Tab Break) field in DocType 'HD Ticket'
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
msgid "Response"
msgstr "واکنش"

#. Label of the response_by (Datetime) field in DocType 'HD Ticket'
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
msgid "Response By"
msgstr "پاسخ توسط"

#. Label of the response_options_sb (Section Break) field in DocType 'HD
#. Support Search Source'
#: helpdesk/helpdesk/doctype/hd_support_search_source/hd_support_search_source.json
msgid "Response Options"
msgstr "گزینه‌های پاسخ"

#. Label of the response_result_key_path (Data) field in DocType 'HD Support
#. Search Source'
#: helpdesk/helpdesk/doctype/hd_support_search_source/hd_support_search_source.json
msgid "Response Result Key Path"
msgstr "مسیر کلیدی نتیجه پاسخ"

#: helpdesk/helpdesk/doctype/hd_service_level_agreement/hd_service_level_agreement.py:65
msgid "Response Time for {0} priority in row {1} can't be greater than Resolution Time."
msgstr "زمان پاسخ برای اولویت {0} در ردیف {1} نمی‌تواند بیشتر از زمان حل و فصل باشد."

#. Label of the response_and_resolution_time_section (Section Break) field in
#. DocType 'HD Service Level Agreement'
#: helpdesk/helpdesk/doctype/hd_service_level_agreement/hd_service_level_agreement.json
msgid "Response and Resolution"
msgstr "پاسخ و حل و فصل"

#. Label of the assign_within_team (Check) field in DocType 'HD Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "Restrict agent assignment to selected Team"
msgstr ""

#. Label of the restrict_tickets_by_agent_group (Check) field in DocType 'HD
#. Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "Restrict tickets by Team"
msgstr ""

#. Label of the result_preview_field (Data) field in DocType 'HD Support Search
#. Source'
#: helpdesk/helpdesk/doctype/hd_support_search_source/hd_support_search_source.json
msgid "Result Preview Field"
msgstr "فیلد پیش نمایش نتایج"

#. Label of the result_route_field (Data) field in DocType 'HD Support Search
#. Source'
#: helpdesk/helpdesk/doctype/hd_support_search_source/hd_support_search_source.json
msgid "Result Route Field"
msgstr "فیلد مسیر نتیجه"

#. Label of the result_title_field (Data) field in DocType 'HD Support Search
#. Source'
#: helpdesk/helpdesk/doctype/hd_support_search_source/hd_support_search_source.json
msgid "Result Title Field"
msgstr "فیلد عنوان نتیجه"

#. Label of the route_name (Data) field in DocType 'HD View'
#: helpdesk/helpdesk/doctype/hd_view/hd_view.json
msgid "Route Name"
msgstr ""

#. Label of the rows (Code) field in DocType 'HD View'
#: helpdesk/helpdesk/doctype/hd_view/hd_view.json
msgid "Rows"
msgstr ""

#. Label of the sla (Link) field in DocType 'HD Ticket'
#. Label of the sla_tab (Tab Break) field in DocType 'HD Ticket'
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
msgid "SLA"
msgstr ""

#. Label of the service_level_agreement_creation (Datetime) field in DocType
#. 'HD Ticket'
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
msgid "SLA Creation"
msgstr ""

#. Label of the sla_mapping_section (Section Break) field in DocType 'HD Ticket
#. Status'
#: helpdesk/helpdesk/doctype/hd_ticket_status/hd_ticket_status.json
msgid "SLA Mapping"
msgstr ""

#. Label of the agreement_status (Select) field in DocType 'HD Ticket'
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
msgid "SLA Status"
msgstr "وضعیت SLA"

#. Option for the 'Workday' (Select) field in DocType 'HD Service Day'
#. Option for the 'Weekly Off' (Select) field in DocType 'HD Service Holiday
#. List'
#: helpdesk/helpdesk/doctype/hd_service_day/hd_service_day.json
#: helpdesk/helpdesk/doctype/hd_service_holiday_list/hd_service_holiday_list.json
msgid "Saturday"
msgstr "شنبه"

#: desk/src/components/Settings/Assignment Rules/AssignmentRuleView.vue:42
#: desk/src/components/Settings/EmailNotifications/Notification.vue:50
msgid "Save"
msgstr "ذخیره"

#. Label of the script (Code) field in DocType 'HD Form Script'
#: helpdesk/helpdesk/doctype/hd_form_script/hd_form_script.json
msgid "Script"
msgstr "اسکریپت"

#. Label of the search_tab (Tab Break) field in DocType 'HD Settings'
#: desk/src/components/Settings/Assignment Rules/AssigneeSearch.vue:26
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "Search"
msgstr "جستجو کردن"

#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.js:10
msgid "Search Index Regenerated"
msgstr ""

#: helpdesk/helpdesk/report/ticket_search_analysis/ticket_search_analysis.py:40
msgid "Search Score"
msgstr ""

#. Label of the search_term_param_name (Data) field in DocType 'HD Support
#. Search Source'
#: helpdesk/helpdesk/doctype/hd_support_search_source/hd_support_search_source.json
msgid "Search Term Param Name"
msgstr "عبارت جستجو نام Param"

#: helpdesk/helpdesk/doctype/hd_service_level_agreement/hd_service_level_agreement.py:157
msgid "Select a Default Priority."
msgstr "یک اولویت پیش‌فرض را انتخاب کنید."

#: helpdesk/helpdesk/doctype/hd_service_holiday_list/hd_service_holiday_list.js:41
msgid "Select your weekly off day"
msgstr "روز تعطیل هفتگی خود را انتخاب کنید"

#. Label of the send_email_feedback_on_status (Link) field in DocType 'HD
#. Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "Send feedback when"
msgstr ""

#: desk/src/components/Settings/EmailNotifications/NotificationList.vue:78
msgid "Sent to all of the assigned agents after a reply from one of the contacts"
msgstr ""

#: desk/src/components/Settings/EmailNotifications/NotificationList.vue:85
msgid "Sent to all of the recipients associated with an agent's reply"
msgstr ""

#: desk/src/components/Settings/EmailNotifications/NotificationList.vue:73
msgid "Sent to the user right after creating an email ticket"
msgstr ""

#: desk/src/components/Settings/EmailNotifications/NotificationList.vue:66
msgid "Sent to the user who has raised the ticket after the ticket is closed or resolved"
msgstr ""

#. Label of the service_level (Data) field in DocType 'HD Service Level
#. Agreement'
#: helpdesk/helpdesk/doctype/hd_service_level_agreement/hd_service_level_agreement.json
msgid "Service Level Name"
msgstr "نام سطح خدمات"

#: helpdesk/helpdesk/doctype/hd_service_level_agreement/hd_service_level_agreement.py:56
msgid "Set Resolution Time for Priority {0} in row {1}."
msgstr ""

#: helpdesk/helpdesk/doctype/hd_service_level_agreement/hd_service_level_agreement.py:48
msgid "Set Response Time for Priority {0} in row {1}."
msgstr "زمان پاسخ را برای اولویت {0} در ردیف {1} تنظیم کنید."

#: helpdesk/helpdesk/doctype/hd_service_level_agreement/hd_service_level_agreement.py:124
msgid "Setting <strong>{0}</strong> as the default SLA removes <strong>{1}</strong> as the default SLA. You’ll need to add a condition in <strong>{1}</strong> for the SLA to work."
msgstr ""

#. Label of the column_break_feto (Column Break) field in DocType 'HD Team'
#: helpdesk/helpdesk/doctype/hd_team/hd_team.json
msgid "Settings"
msgstr "تنظیمات"

#. Label of the setup_section (Section Break) field in DocType 'HD Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "Setup"
msgstr "تنظیمات"

#. Label of the share_feedback_section (Section Break) field in DocType 'HD
#. Settings'
#: desk/src/components/Settings/EmailNotifications/NotificationList.vue:65
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "Share Feedback"
msgstr "اشتراک گذاری بازخورد"

#. Label of the different_view (Check) field in DocType 'HD Ticket Status'
#: helpdesk/helpdesk/doctype/hd_ticket_status/hd_ticket_status.json
msgid "Show end users a different view"
msgstr ""

#. Description of the 'Condition' (Code) field in DocType 'HD Service Level
#. Agreement'
#: helpdesk/helpdesk/doctype/hd_service_level_agreement/hd_service_level_agreement.json
msgid "Simple Python Expression, Example: doc.status == 'Open' and doc.ticket_type == 'Bug'"
msgstr "عبارت ساده پایتون، مثال: doc.status == 'Open' و doc.ticket_type == 'Bug'"

#. Label of the skip_email_workflow (Check) field in DocType 'HD Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "Skip e-mail workflow"
msgstr ""

#. Label of the source_doctype (Link) field in DocType 'HD Support Search
#. Source'
#: helpdesk/helpdesk/doctype/hd_support_search_source/hd_support_search_source.json
msgid "Source DocType"
msgstr "منبع DocType"

#. Label of the source_name (Data) field in DocType 'HD Support Search Source'
#: helpdesk/helpdesk/doctype/hd_support_search_source/hd_support_search_source.json
msgid "Source Name"
msgstr "نام منبع"

#. Label of the source_type (Select) field in DocType 'HD Support Search
#. Source'
#: helpdesk/helpdesk/doctype/hd_support_search_source/hd_support_search_source.json
msgid "Source Type"
msgstr "نوع منبع"

#: helpdesk/api/knowledge_base.py:119
msgid "Source and target category cannot be same"
msgstr ""

#: helpdesk/helpdesk/doctype/hd_ticket/api.py:249
msgid "Source and target ticket cannot be same"
msgstr ""

#: helpdesk/helpdesk/doctype/hd_ticket/api.py:245
msgid "Source ticket does not exist"
msgstr ""

#. Label of the split_and_merge_section (Section Break) field in DocType 'HD
#. Ticket'
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
msgid "Split and Merge"
msgstr ""

#: helpdesk/helpdesk/doctype/hd_form_script/hd_form_script.js:9
msgid "Standard Form Scripts can't be modified, duplicate the script instead."
msgstr ""

#. Label of the start_date (Date) field in DocType 'HD Service Level Agreement'
#: helpdesk/helpdesk/doctype/hd_service_level_agreement/hd_service_level_agreement.json
msgid "Start Date"
msgstr "تاریخ شروع"

#. Label of the start_time (Time) field in DocType 'HD Service Day'
#: helpdesk/helpdesk/doctype/hd_service_day/hd_service_day.json
msgid "Start Time"
msgstr "زمان شروع"

#: helpdesk/helpdesk/doctype/hd_service_level_agreement/hd_service_level_agreement.py:99
msgid "Start Time can't be greater than or equal to End Time in row <u>{0}</u>."
msgstr ""

#. Label of the status (Select) field in DocType 'HD Article'
#. Label of the status_section (Section Break) field in DocType 'HD Settings'
#. Label of the status (Link) field in DocType 'HD Ticket'
#: helpdesk/helpdesk/doctype/hd_article/hd_article.json
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
#: helpdesk/helpdesk/report/ticket_analytics/ticket_analytics.js:44
#: helpdesk/helpdesk/report/ticket_summary/ticket_summary.js:31
msgid "Status"
msgstr "وضعیت"

#. Label of the auto_close_status (Link) field in DocType 'HD Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "Status "
msgstr "وضعیت "

#. Label of the status_category (Data) field in DocType 'HD Ticket'
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
msgid "Status Category"
msgstr ""

#. Label of the status_details (Section Break) field in DocType 'HD Service
#. Level Agreement'
#: helpdesk/helpdesk/doctype/hd_service_level_agreement/hd_service_level_agreement.json
msgid "Status Details"
msgstr "جزئیات وضعیت"

#. Description of the 'Ticket Reopen status' (Link) field in DocType 'HD
#. Service Level Agreement'
#: helpdesk/helpdesk/doctype/hd_service_level_agreement/hd_service_level_agreement.json
msgid "Status of the ticket, when a customer replies on the ticket with this SLA.\n\n"
"If not selected, the value will be takes from HD Settings DocType."
msgstr ""

#. Description of the 'Ticket Reopen status' (Link) field in DocType 'HD
#. Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "Status of the ticket, when a customer replies on the ticket."
msgstr ""

#. Description of the 'Default Ticket Status' (Link) field in DocType 'HD
#. Service Level Agreement'
#: helpdesk/helpdesk/doctype/hd_service_level_agreement/hd_service_level_agreement.json
msgid "Status of the ticket, when it is created in the system with this SLA.\n"
"If not selected, the value will be taken from HD Settings DocType"
msgstr ""

#. Description of the 'Default ticket status' (Link) field in DocType 'HD
#. Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "Status of the ticket, when it is created in the system."
msgstr ""

#: helpdesk/templates/components/contact_with_us.html:7
msgid "Still got questions? We're here to assist you."
msgstr ""

#. Label of the subject (Data) field in DocType 'HD Ticket'
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
#: helpdesk/helpdesk/report/ticket_search_analysis/ticket_search_analysis.py:30
msgid "Subject"
msgstr "موضوع"

#. Label of the subject_weight (Int) field in DocType 'HD Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "Subject Weight"
msgstr ""

#. Label of the summary (Text Editor) field in DocType 'HD Ticket'
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
msgid "Summary"
msgstr "خلاصه"

#. Option for the 'Workday' (Select) field in DocType 'HD Service Day'
#. Option for the 'Weekly Off' (Select) field in DocType 'HD Service Holiday
#. List'
#: helpdesk/helpdesk/doctype/hd_service_day/hd_service_day.json
#: helpdesk/helpdesk/doctype/hd_service_holiday_list/hd_service_holiday_list.json
msgid "Sunday"
msgstr "یک‌شنبه"

#. Name of a report
#: helpdesk/helpdesk/report/support_hour_distribution/support_hour_distribution.json
msgid "Support Hour Distribution"
msgstr "توزیع ساعت پشتیبانی"

#. Label of a Link in the Helpdesk Workspace
#: helpdesk/helpdesk/workspace/helpdesk/helpdesk.json
msgid "Support Policy"
msgstr ""

#. Name of a role
#: helpdesk/helpdesk/doctype/hd_service_holiday_list/hd_service_holiday_list.json
#: helpdesk/helpdesk/doctype/hd_ticket_type/hd_ticket_type.json
msgid "Support Team"
msgstr "تیم پشتیبانی"

#. Label of the synonyms (Table) field in DocType 'HD Synonyms'
#: helpdesk/helpdesk/doctype/hd_synonyms/hd_synonyms.json
msgid "Synonyms"
msgstr ""

#. Label of the is_system (Check) field in DocType 'HD Ticket Type'
#: helpdesk/helpdesk/doctype/hd_ticket_type/hd_ticket_type.json
msgid "System"
msgstr "سیستم"

#. Name of a role
#: helpdesk/helpdesk/doctype/hd_action/hd_action.json
#: helpdesk/helpdesk/doctype/hd_agent/hd_agent.json
#: helpdesk/helpdesk/doctype/hd_article/hd_article.json
#: helpdesk/helpdesk/doctype/hd_article_category/hd_article_category.json
#: helpdesk/helpdesk/doctype/hd_article_feedback/hd_article_feedback.json
#: helpdesk/helpdesk/doctype/hd_canned_response/hd_canned_response.json
#: helpdesk/helpdesk/doctype/hd_customer/hd_customer.json
#: helpdesk/helpdesk/doctype/hd_desk_account_request/hd_desk_account_request.json
#: helpdesk/helpdesk/doctype/hd_email_feedback/hd_email_feedback.json
#: helpdesk/helpdesk/doctype/hd_escalation_rule/hd_escalation_rule.json
#: helpdesk/helpdesk/doctype/hd_form_script/hd_form_script.json
#: helpdesk/helpdesk/doctype/hd_notification/hd_notification.json
#: helpdesk/helpdesk/doctype/hd_organization/hd_organization.json
#: helpdesk/helpdesk/doctype/hd_portal_signup_request/hd_portal_signup_request.json
#: helpdesk/helpdesk/doctype/hd_service_holiday_list/hd_service_holiday_list.json
#: helpdesk/helpdesk/doctype/hd_service_level_agreement/hd_service_level_agreement.json
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
#: helpdesk/helpdesk/doctype/hd_stopword/hd_stopword.json
#: helpdesk/helpdesk/doctype/hd_synonyms/hd_synonyms.json
#: helpdesk/helpdesk/doctype/hd_team/hd_team.json
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
#: helpdesk/helpdesk/doctype/hd_ticket_activity/hd_ticket_activity.json
#: helpdesk/helpdesk/doctype/hd_ticket_comment/hd_ticket_comment.json
#: helpdesk/helpdesk/doctype/hd_ticket_feedback_option/hd_ticket_feedback_option.json
#: helpdesk/helpdesk/doctype/hd_ticket_priority/hd_ticket_priority.json
#: helpdesk/helpdesk/doctype/hd_ticket_status/hd_ticket_status.json
#: helpdesk/helpdesk/doctype/hd_ticket_template/hd_ticket_template.json
#: helpdesk/helpdesk/doctype/hd_ticket_type/hd_ticket_type.json
#: helpdesk/helpdesk/doctype/hd_view/hd_view.json
msgid "System Manager"
msgstr "مدیر سیستم"

#: helpdesk/helpdesk/doctype/hd_ticket_type/hd_ticket_type.py:12
msgid "System types can not be deleted"
msgstr ""

#: helpdesk/helpdesk/doctype/hd_email_feedback/hd_email_feedback.py:22
msgid "Tampered Access"
msgstr ""

#: helpdesk/helpdesk/doctype/hd_ticket/api.py:247
msgid "Target ticket does not exist"
msgstr ""

#. Option for the 'Color' (Select) field in DocType 'HD Ticket Status'
#: helpdesk/helpdesk/doctype/hd_ticket_status/hd_ticket_status.json
msgid "Teal"
msgstr "فیروزه‌ای"

#. Label of the team (Link) field in DocType 'HD Escalation Rule'
#. Label of the to_team (Link) field in DocType 'HD Escalation Rule'
#. Label of the agent_group (Link) field in DocType 'HD Ticket'
#. Label of a Link in the Helpdesk Workspace
#: helpdesk/helpdesk/doctype/hd_escalation_rule/hd_escalation_rule.json
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
#: helpdesk/helpdesk/workspace/helpdesk/helpdesk.json
msgid "Team"
msgstr "تیم"

#. Label of the agent_groups_section (Section Break) field in DocType 'HD
#. Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "Team Restrictions"
msgstr ""

#. Label of the template (Link) field in DocType 'HD Ticket'
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
msgid "Template"
msgstr "قالب"

#. Label of the template_name (Data) field in DocType 'HD Ticket Template'
#: helpdesk/helpdesk/doctype/hd_ticket_template/hd_ticket_template.json
msgid "Template Name"
msgstr "نام الگو"

#: helpdesk/helpdesk/doctype/hd_ticket_status/hd_ticket_status.py:12
msgid "The 'Closed' status cannot be renamed."
msgstr "وضعیت «بسته» قابل تغییر نام نیست."

#: helpdesk/helpdesk/doctype/hd_service_level_agreement/hd_service_level_agreement.py:147
msgid "The Condition '{0}' is invalid: {1}"
msgstr "شرط '{0}' نامعتبر است: {1}"

#: helpdesk/helpdesk/doctype/hd_service_holiday_list/hd_service_holiday_list.py:54
msgid "The holiday on {0} is not between From Date and To Date"
msgstr "تعطیلات در {0} بین از تاریخ و تا تاریخ نیست"

#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.py:40
msgid "The status for sending feedback must be of <u>Resolved</u> category."
msgstr ""

#. Description of a DocType
#: helpdesk/helpdesk/doctype/hd_ticket_template/hd_ticket_template.json
msgid "This doctype will be deprecated in the future"
msgstr ""

#. Description of the 'Instantly send e-mail' (Check) field in DocType 'HD
#. Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "This field is used to send an email instantly without adding it into the queue. If this field is checked, the email will be sent immediately after clicking the \"Send\" button, instead of being added to the email queue for later processing."
msgstr ""

#. Description of the 'Skip e-mail workflow' (Check) field in DocType 'HD
#. Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "This field is used to skip email-related workflows for tickets. If this field is checked, no emails will be sent related to tickets, such as new ticket creation, status updates, or notifications."
msgstr ""

#: helpdesk/www/helpdesk/index.py:23
msgid "This method is only meant for developer mode"
msgstr ""

#: helpdesk/helpdesk/doctype/hd_ticket/api.py:283
msgid "This ticket (#{0}) has been merged with ticket <a href = '/helpdesk/tickets/{1}'>#{1}</a>."
msgstr ""

#: helpdesk/helpdesk/doctype/hd_ticket/api.py:425
msgid "This ticket has been split to a new ticket. Please follow up on ticket <a href={0}>#{1}</a>."
msgstr ""

#. Option for the 'Workday' (Select) field in DocType 'HD Service Day'
#. Option for the 'Weekly Off' (Select) field in DocType 'HD Service Holiday
#. List'
#: helpdesk/helpdesk/doctype/hd_service_day/hd_service_day.json
#: helpdesk/helpdesk/doctype/hd_service_holiday_list/hd_service_holiday_list.json
msgid "Thursday"
msgstr "پنج‌شنبه"

#. Label of the ticket (Link) field in DocType 'HD Email Feedback'
#. Label of the reference_ticket (Link) field in DocType 'HD Notification'
#. Label of the ticket_tab (Tab Break) field in DocType 'HD Settings'
#: helpdesk/helpdesk/doctype/hd_email_feedback/hd_email_feedback.json
#: helpdesk/helpdesk/doctype/hd_notification/hd_notification.json
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "Ticket"
msgstr ""

#. Name of a report
#. Label of a Link in the Helpdesk Workspace
#: helpdesk/helpdesk/report/ticket_analytics/ticket_analytics.json
#: helpdesk/helpdesk/workspace/helpdesk/helpdesk.json
msgid "Ticket Analytics"
msgstr ""

#. Label of a Card Break in the Helpdesk Workspace
#: helpdesk/helpdesk/workspace/helpdesk/helpdesk.json
msgid "Ticket Configuration"
msgstr ""

#. Label of the is_merged (Check) field in DocType 'HD Ticket'
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
msgid "Ticket Merged"
msgstr ""

#: helpdesk/helpdesk/doctype/hd_email_feedback/hd_email_feedback.py:40
msgid "Ticket Not Found"
msgstr ""

#: helpdesk/helpdesk/report/ticket_analytics/ticket_analytics.js:50
#: helpdesk/helpdesk/report/ticket_analytics/ticket_analytics.py:77
#: helpdesk/helpdesk/report/ticket_summary/ticket_summary.js:37
#: helpdesk/helpdesk/report/ticket_summary/ticket_summary.py:105
msgid "Ticket Priority"
msgstr ""

#. Label of the ticket_reopen_status (Link) field in DocType 'HD Service Level
#. Agreement'
#. Label of the ticket_reopen_status (Link) field in DocType 'HD Settings'
#: helpdesk/helpdesk/doctype/hd_service_level_agreement/hd_service_level_agreement.json
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "Ticket Reopen status"
msgstr ""

#: desk/src/components/Settings/Assignment Rules/AssigneeRules.vue:18
msgid "Ticket Routing"
msgstr ""

#. Label of the ticket_split_from (Link) field in DocType 'HD Ticket'
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
msgid "Ticket Split From"
msgstr ""

#. Name of a report
#. Label of a Link in the Helpdesk Workspace
#: helpdesk/helpdesk/report/ticket_summary/ticket_summary.json
#: helpdesk/helpdesk/workspace/helpdesk/helpdesk.json
msgid "Ticket Summary"
msgstr ""

#. Label of the ticket_type_section (Section Break) field in DocType 'HD
#. Settings'
#. Label of the ticket_type (Link) field in DocType 'HD Ticket'
#. Label of a Link in the Helpdesk Workspace
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
#: helpdesk/helpdesk/report/ticket_analytics/ticket_analytics.py:66
#: helpdesk/helpdesk/report/ticket_summary/ticket_summary.py:94
#: helpdesk/helpdesk/workspace/helpdesk/helpdesk.json
msgid "Ticket Type"
msgstr ""

#: helpdesk/helpdesk/doctype/hd_email_feedback/hd_email_feedback.py:21
msgid "Ticket does not exist."
msgstr ""

#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.py:318
msgid "Ticket must be resolved with a feedback"
msgstr ""

#: helpdesk/helpdesk/doctype/hd_ticket/api.py:45
msgid "Ticket not found"
msgstr ""

#: helpdesk/helpdesk/doctype/hd_email_feedback/hd_email_feedback.py:40
msgid "Ticket not found for the provided key."
msgstr ""

#. Label of the ticket_type (Link) field in DocType 'HD Escalation Rule'
#. Label of the to_ticket_type (Link) field in DocType 'HD Escalation Rule'
#: helpdesk/helpdesk/doctype/hd_escalation_rule/hd_escalation_rule.json
msgid "Ticket type"
msgstr ""

#. Label of the is_ticket_type_mandatory (Check) field in DocType 'HD Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.py:295
msgid "Ticket type is mandatory"
msgstr ""

#. Name of a report
#: helpdesk/helpdesk/report/ticket_search_analysis/ticket_search_analysis.json
msgid "Ticket-Search Analysis"
msgstr ""

#: helpdesk/api/ticket.py:18
msgid "Tickets can only be assigned to agents"
msgstr ""

#. Label of the title (Data) field in DocType 'HD Article'
#. Label of the title (Data) field in DocType 'HD Canned Response'
#: helpdesk/helpdesk/doctype/hd_article/hd_article.json
#: helpdesk/helpdesk/doctype/hd_canned_response/hd_canned_response.json
msgid "Title"
msgstr "عنوان"

#. Label of the title_slug (Data) field in DocType 'HD Article'
#: helpdesk/helpdesk/doctype/hd_article/hd_article.json
msgid "Title Slug"
msgstr ""

#. Label of the user_to (Link) field in DocType 'HD Notification'
#: helpdesk/helpdesk/doctype/hd_notification/hd_notification.json
msgid "To"
msgstr "به"

#. Label of the to_date (Date) field in DocType 'HD Service Holiday List'
#: helpdesk/helpdesk/doctype/hd_service_holiday_list/hd_service_holiday_list.json
#: helpdesk/helpdesk/report/first_response_time_for_tickets/first_response_time_for_tickets.js:16
#: helpdesk/helpdesk/report/support_hour_distribution/support_hour_distribution.js:15
#: helpdesk/helpdesk/report/ticket_analytics/ticket_analytics.js:24
#: helpdesk/helpdesk/report/ticket_summary/ticket_summary.js:24
msgid "To Date"
msgstr "تا تاریخ"

#: helpdesk/helpdesk/doctype/hd_service_holiday_list/hd_service_holiday_list.py:45
msgid "To Date cannot be before From Date"
msgstr "تا تاریخ نمی‌تواند قبل از از تاریخ باشد"

#: helpdesk/helpdesk/report/ticket_search_analysis/ticket_search_analysis.py:35
msgid "Top Result"
msgstr ""

#: helpdesk/helpdesk/report/ticket_analytics/ticket_analytics.py:98
msgid "Total"
msgstr "جمع"

#. Label of the total_hold_time (Duration) field in DocType 'HD Ticket'
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
msgid "Total Hold Time"
msgstr "کل زمان نگهداری"

#. Label of the total_holidays (Int) field in DocType 'HD Service Holiday List'
#: helpdesk/helpdesk/doctype/hd_service_holiday_list/hd_service_holiday_list.json
msgid "Total Holidays"
msgstr "کل تعطیلات"

#: helpdesk/helpdesk/report/ticket_summary/ticket_summary.py:126
msgid "Total Ticket"
msgstr ""

#. Option for the 'Workday' (Select) field in DocType 'HD Service Day'
#. Option for the 'Weekly Off' (Select) field in DocType 'HD Service Holiday
#. List'
#: helpdesk/helpdesk/doctype/hd_service_day/hd_service_day.json
#: helpdesk/helpdesk/doctype/hd_service_holiday_list/hd_service_holiday_list.json
msgid "Tuesday"
msgstr "سه‌شنبه"

#. Label of the notification_type (Select) field in DocType 'HD Notification'
#. Label of the type (Select) field in DocType 'HD View'
#: helpdesk/helpdesk/doctype/hd_notification/hd_notification.json
#: helpdesk/helpdesk/doctype/hd_view/hd_view.json
msgid "Type"
msgstr "نوع"

#. Label of the url_method (Data) field in DocType 'HD Ticket Template Field'
#: helpdesk/helpdesk/doctype/hd_ticket_template_field/hd_ticket_template_field.json
msgid "URL/Method"
msgstr ""

#: desk/src/components/Settings/Assignment Rules/AssignmentRuleView.vue:204
msgid "Unassignment condition"
msgstr ""

#: helpdesk/helpdesk/doctype/hd_email_feedback/hd_email_feedback.py:16
msgid "Unauthorized Access"
msgstr "دسترسی غیرمجاز"

#: helpdesk/helpdesk/doctype/hd_email_feedback/hd_email_feedback.py:16
msgid "Unauthorized access."
msgstr ""

#: desk/src/components/Settings/Assignment Rules/AssignmentRuleView.vue:28
#: desk/src/components/Settings/EmailNotifications/Notification.vue:30
msgid "Unsaved"
msgstr ""

#: desk/src/components/Settings/EmailNotifications/Notification.vue:112
#: desk/src/components/Settings/SettingsModal.vue:42
msgid "Unsaved changes"
msgstr "تغییرات ذخیره نشده"

#. Label of the update_status_to (Link) field in DocType 'HD Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "Update status to"
msgstr "به‌روزرسانی وضعیت به"

#. Label of the user (Link) field in DocType 'HD Agent'
#. Label of the user (Link) field in DocType 'HD Article Feedback'
#. Label of the user (Link) field in DocType 'HD Desk Account Request'
#. Label of the user (Link) field in DocType 'HD Portal Signup Request'
#. Label of the user (Link) field in DocType 'HD Team Member'
#. Label of the user (Link) field in DocType 'HD View'
#: helpdesk/helpdesk/doctype/hd_agent/hd_agent.json
#: helpdesk/helpdesk/doctype/hd_article_feedback/hd_article_feedback.json
#: helpdesk/helpdesk/doctype/hd_desk_account_request/hd_desk_account_request.json
#: helpdesk/helpdesk/doctype/hd_portal_signup_request/hd_portal_signup_request.json
#: helpdesk/helpdesk/doctype/hd_team_member/hd_team_member.json
#: helpdesk/helpdesk/doctype/hd_view/hd_view.json
#: helpdesk/helpdesk/report/ticket_analytics/ticket_analytics.py:55
#: helpdesk/helpdesk/report/ticket_summary/ticket_summary.py:83
msgid "User"
msgstr "کاربر"

#: helpdesk/helpdesk/doctype/hd_team/hd_team.py:133
msgid "User Not found"
msgstr "کاربر یافت نشد"

#. Label of the user_resolution_time (Duration) field in DocType 'HD Ticket'
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
msgid "User Resolution Time"
msgstr "زمان حل و فصل کاربر"

#. Label of the users (Table MultiSelect) field in DocType 'HD Team'
#: helpdesk/helpdesk/doctype/hd_team/hd_team.json
msgid "Users"
msgstr "کاربران"

#. Label of the agreement_details_section (Section Break) field in DocType 'HD
#. Service Level Agreement'
#: helpdesk/helpdesk/doctype/hd_service_level_agreement/hd_service_level_agreement.json
msgid "Valid From"
msgstr "معتبر از"

#. Label of the via_customer_portal (Check) field in DocType 'HD Ticket'
#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.json
msgid "Via Customer Portal"
msgstr "از طریق پورتال مشتری"

#. Label of the views (Int) field in DocType 'HD Article'
#: helpdesk/helpdesk/doctype/hd_article/hd_article.json
msgid "Views"
msgstr "بازدیدها"

#. Option for the 'Color' (Select) field in DocType 'HD Ticket Status'
#: helpdesk/helpdesk/doctype/hd_ticket_status/hd_ticket_status.json
msgid "Violet"
msgstr "بنفش"

#. Option for the 'Workday' (Select) field in DocType 'HD Service Day'
#. Option for the 'Weekly Off' (Select) field in DocType 'HD Service Holiday
#. List'
#: helpdesk/helpdesk/doctype/hd_service_day/hd_service_day.json
#: helpdesk/helpdesk/doctype/hd_service_holiday_list/hd_service_holiday_list.json
msgid "Wednesday"
msgstr "چهارشنبه"

#: helpdesk/helpdesk/report/ticket_analytics/ticket_analytics.js:34
msgid "Weekly"
msgstr "هفتگی"

#. Label of the weekly_off (Check) field in DocType 'HD Holiday'
#. Label of the weekly_off (Select) field in DocType 'HD Service Holiday List'
#: helpdesk/helpdesk/doctype/hd_holiday/hd_holiday.json
#: helpdesk/helpdesk/doctype/hd_service_holiday_list/hd_service_holiday_list.json
msgid "Weekly Off"
msgstr "تعطیلات هفتگی"

#. Description of the 'Auto update status' (Check) field in DocType 'HD
#. Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "When enabled, the ticket status will automatically change to the status selected below whenever the agent responds to a ticket.\n"
msgstr "وقتی فعال باشد، هر زمان که اپراتور به یک تیکت پاسخ دهد، وضعیت تیکت به طور خودکار به وضعیت انتخاب شده در زیر تغییر می‌کند.\n"

#. Label of the word (Data) field in DocType 'HD Stopword'
#. Label of the word (Data) field in DocType 'HD Synonyms'
#: helpdesk/helpdesk/doctype/hd_stopword/hd_stopword.json
#: helpdesk/helpdesk/doctype/hd_synonyms/hd_synonyms.json
msgid "Word"
msgstr "واژه"

#. Label of the workday (Select) field in DocType 'HD Service Day'
#: helpdesk/helpdesk/doctype/hd_service_day/hd_service_day.json
msgid "Workday"
msgstr "روز کاری"

#: helpdesk/helpdesk/doctype/hd_service_level_agreement/hd_service_level_agreement.py:110
msgid "Workday {0} has been repeated."
msgstr "روز کاری {0} تکرار شده است."

#. Label of the workflow_tab (Tab Break) field in DocType 'HD Settings'
#: helpdesk/helpdesk/doctype/hd_settings/hd_settings.json
msgid "Workflow"
msgstr "گردش کار"

#. Label of the support_and_resolution_section_break (Section Break) field in
#. DocType 'HD Service Level Agreement'
#. Label of the support_and_resolution (Table) field in DocType 'HD Service
#. Level Agreement'
#: helpdesk/helpdesk/doctype/hd_service_level_agreement/hd_service_level_agreement.json
msgid "Working Hours"
msgstr "ساعات کاری"

#: helpdesk/helpdesk/report/ticket_analytics/ticket_analytics.js:37
msgid "Yearly"
msgstr "سالانه"

#. Option for the 'Color' (Select) field in DocType 'HD Ticket Status'
#: helpdesk/helpdesk/doctype/hd_ticket_status/hd_ticket_status.json
msgid "Yellow"
msgstr "زرد"

#: helpdesk/api/dashboard.py:18
msgid "You are not allowed to view this dashboard data."
msgstr ""

#: helpdesk/utils.py:158
msgid "You are not permitted to access this resource."
msgstr "شما مجاز به دسترسی به این منبع نیستید."

#: helpdesk/helpdesk/doctype/hd_ticket/hd_ticket.py:530
msgid "You are not permitted to add a comment"
msgstr ""

#: helpdesk/helpdesk/doctype/hd_service_level_agreement/hd_service_level_agreement.py:137
msgid "You cannot disable the default SLA."
msgstr ""

#: helpdesk/helpdesk/doctype/hd_service_level_agreement/hd_service_level_agreement.py:41
msgid "You cannot set more than one Default Priority."
msgstr ""

#: helpdesk/helpdesk/doctype/hd_email_feedback/hd_email_feedback.py:29
msgid "You have already provided feedback for this ticket."
msgstr ""

#: helpdesk/helpdesk/doctype/hd_service_level_agreement/hd_service_level_agreement.py:131
msgid "You must set one SLA as Default. Please check the Default SLA option."
msgstr ""

#: desk/src/components/Settings/EmailNotifications/Notification.vue:17
msgid "back to email event list"
msgstr "بازگشت به لیست رویدادهای ایمیل"

#: desk/src/components/Settings/EmailNotifications/NotificationList.vue:46
msgid "customize {0}"
msgstr "سفارشی‌سازی {0}"

#. Option for the 'Type' (Select) field in DocType 'HD View'
#: helpdesk/helpdesk/doctype/hd_view/hd_view.json
msgid "group_by"
msgstr "group_by"

#: desk/src/components/Settings/EmailNotifications/Notification.vue:91
msgid "here"
msgstr "اینجا"

#. Option for the 'Type' (Select) field in DocType 'HD View'
#: helpdesk/helpdesk/doctype/hd_view/hd_view.json
msgid "kanban"
msgstr "کانبان"

#. Option for the 'Type' (Select) field in DocType 'HD View'
#: helpdesk/helpdesk/doctype/hd_view/hd_view.json
msgid "list"
msgstr "فهرست"

#. Option for the 'Color' (Select) field in DocType 'HD Ticket Status'
#: helpdesk/helpdesk/doctype/hd_ticket_status/hd_ticket_status.json
msgid "purple"
msgstr "بنفش"

#. Label of the synonym (Data) field in DocType 'HD Synonym'
#: helpdesk/helpdesk/doctype/hd_synonym/hd_synonym.json
msgid "synonym"
msgstr ""

