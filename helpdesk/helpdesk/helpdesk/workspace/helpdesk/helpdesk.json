{"charts": [], "content": "[{\"type\":\"header\",\"data\":{\"text\":\"<span class=\\\"h3\\\" style=\\\"\\\"><a href=\\\"/helpdesk\\\">Visit Helpdesk</a></span>\",\"col\":12}},{\"type\":\"spacer\",\"data\":{\"col\":12}},{\"type\":\"paragraph\",\"data\":{\"text\":\"<span class=\\\"h4\\\">Helpdesk Configuration<br></span>\",\"col\":12}},{\"type\":\"card\",\"data\":{\"card_name\":\"Ticket Configuration\",\"col\":4}},{\"type\":\"card\",\"data\":{\"card_name\":\"Agent Configuration\",\"col\":4}},{\"type\":\"card\",\"data\":{\"card_name\":\"Helpdesk Reports\",\"col\":4}},{\"type\":\"card\",\"data\":{\"card_name\":\"Automation\",\"col\":4}},{\"type\":\"card\",\"data\":{\"card_name\":\"Channels\",\"col\":4}}]", "creation": "2022-09-17 11:48:26.825712", "docstatus": 0, "doctype": "Workspace", "for_user": "", "hide_custom": 0, "icon": "support", "idx": 0, "label": "Helpdesk", "links": [{"hidden": 0, "is_query_report": 0, "label": "Agent Configuration", "link_count": 2, "onboard": 0, "type": "Card Break"}, {"hidden": 0, "is_query_report": 0, "label": "Agent", "link_count": 0, "link_to": "HD Agent", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Team", "link_count": 0, "link_to": "HD Team", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Automation", "link_count": 2, "onboard": 0, "type": "Card Break"}, {"hidden": 0, "is_query_report": 0, "label": "Notification Settings", "link_count": 0, "link_to": "Notification Settings", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Assignment Rule", "link_count": 0, "link_to": "Assignment Rule", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Channels", "link_count": 1, "onboard": 0, "type": "Card Break"}, {"hidden": 0, "is_query_report": 0, "label": "<PERSON><PERSON> Account", "link_count": 0, "link_to": "<PERSON><PERSON> Account", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Ticket Configuration", "link_count": 3, "onboard": 0, "type": "Card Break"}, {"hidden": 0, "is_query_report": 0, "label": "Ticket Type", "link_count": 0, "link_to": "HD Ticket Type", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Support Policy", "link_count": 0, "link_to": "HD Service Level Agreement", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "General Settings", "link_count": 0, "link_to": "HD Settings", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Helpdesk Reports", "link_count": 2, "onboard": 0, "type": "Card Break"}, {"hidden": 0, "is_query_report": 0, "label": "Ticket Analytics", "link_count": 0, "link_to": "Ticket Analytics", "link_type": "Report", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Ticket Summary", "link_count": 0, "link_to": "Ticket Summary", "link_type": "Report", "onboard": 0, "type": "Link"}], "modified": "2022-12-02 16:07:39.761267", "modified_by": "Administrator", "module": "Helpdesk", "name": "Helpdesk", "owner": "Administrator", "parent_page": "", "public": 1, "quick_lists": [], "roles": [], "sequence_id": 1, "shortcuts": [], "title": "Helpdesk"}