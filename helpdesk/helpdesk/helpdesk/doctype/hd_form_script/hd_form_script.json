{"actions": [], "allow_rename": 1, "autoname": "prompt", "creation": "2024-06-05 16:14:14.503677", "doctype": "DocType", "engine": "InnoDB", "field_order": ["dt", "apply_to", "enabled", "is_standard", "column_break_lhfw", "apply_to_customer_portal", "apply_on_new_page", "section_break_qhfq", "script"], "fields": [{"default": "HD Ticket", "fieldname": "dt", "fieldtype": "Link", "in_list_view": 1, "label": "DocType", "options": "DocType", "reqd": 1}, {"default": "Form", "fieldname": "apply_to", "fieldtype": "Select", "in_list_view": 1, "label": "Apply To", "options": "Form\nList"}, {"fieldname": "column_break_lhfw", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "enabled", "fieldtype": "Check", "label": "Enabled"}, {"fieldname": "section_break_qhfq", "fieldtype": "Section Break"}, {"default": "function setupForm({doc, updateField, call, router, toast, $dialog, createToast ,applyFilters}) {\n    return {\n        actions: [],\n        onChange:{\n            \"fieldname\":(newVal)=>console.log(newVal)\n        }\n    }\n}", "fieldname": "script", "fieldtype": "Code", "label": "<PERSON><PERSON><PERSON>", "options": "JS"}, {"default": "0", "depends_on": "eval: doc.apply_on_new_page===0", "fieldname": "apply_to_customer_portal", "fieldtype": "Check", "label": "Apply to customer portal"}, {"default": "0", "depends_on": "eval: doc.apply_to_customer_portal===0", "fieldname": "apply_on_new_page", "fieldtype": "Check", "label": "Apply on new page"}, {"default": "0", "depends_on": "eval:frappe.boot.developer_mode", "fieldname": "is_standard", "fieldtype": "Check", "label": "Is Standard", "no_copy": 1}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-08-16 15:45:27.146697", "modified_by": "Administrator", "module": "Helpdesk", "name": "HD Form Script", "naming_rule": "Set by user", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Agent Manager", "select": 1, "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "All", "share": 1}], "row_format": "Dynamic", "sort_field": "creation", "sort_order": "DESC", "states": []}