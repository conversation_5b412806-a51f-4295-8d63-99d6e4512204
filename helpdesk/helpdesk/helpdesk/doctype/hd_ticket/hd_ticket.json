{"actions": [], "allow_import": 1, "autoname": "autoincrement", "creation": "2023-10-17 14:27:11.078679", "doctype": "DocType", "document_type": "Setup", "email_append_to": 1, "engine": "InnoDB", "field_order": ["subject_section", "subject", "raised_by", "status", "priority", "status_category", "cb00", "ticket_type", "agent_group", "summary", "sb_details", "description", "template", "key", "sla_tab", "service_level_section", "sla", "response_by", "cb", "agreement_status", "resolution_by", "service_level_agreement_creation", "on_hold_since", "total_hold_time", "response_tab", "response", "first_response_time", "first_responded_on", "column_break_26", "avg_response_time", "resolution_tab", "section_break_19", "resolution_details", "column_break1", "opening_date", "opening_time", "resolution_date", "resolution_time", "user_resolution_time", "reference_tab", "additional_info", "contact", "customer", "email_account", "column_break_16", "via_customer_portal", "attachment", "content_type", "split_and_merge_section", "is_merged", "merged_with", "ticket_split_from", "feedback_tab", "customer_feedback_section", "feedback_rating", "feedback", "feedback_extra", "meta_tab"], "fields": [{"fieldname": "subject_section", "fieldtype": "Section Break", "options": "fa fa-flag"}, {"bold": 1, "fieldname": "subject", "fieldtype": "Data", "in_global_search": 1, "in_standard_filter": 1, "label": "Subject", "reqd": 1}, {"bold": 1, "depends_on": "eval:doc.__islocal", "fieldname": "raised_by", "fieldtype": "Data", "in_global_search": 1, "in_list_view": 1, "label": "Raised By (Email)", "oldfieldname": "raised_by", "oldfieldtype": "Data", "options": "Email"}, {"fieldname": "status", "fieldtype": "Link", "in_list_view": 1, "label": "Status", "no_copy": 1, "oldfieldname": "status", "oldfieldtype": "Select", "options": "HD Ticket Status", "search_index": 1}, {"fieldname": "priority", "fieldtype": "Link", "in_list_view": 1, "label": "Priority", "options": "HD Ticket Priority"}, {"fieldname": "cb00", "fieldtype": "Column Break"}, {"fieldname": "ticket_type", "fieldtype": "Link", "label": "Ticket Type", "options": "HD Ticket Type"}, {"fieldname": "agent_group", "fieldtype": "Link", "label": "Team", "options": "HD Team"}, {"fieldname": "ticket_split_from", "fieldtype": "Link", "label": "Ticket Split From", "options": "HD Ticket", "read_only": 1}, {"collapsible": 1, "collapsible_depends_on": "eval:doc.status!=\"Closed\"", "fieldname": "sb_details", "fieldtype": "Section Break", "label": "Details"}, {"bold": 1, "fieldname": "description", "fieldtype": "Text Editor", "in_global_search": 1, "label": "Description", "oldfieldname": "problem_description", "oldfieldtype": "Text"}, {"fieldname": "template", "fieldtype": "Link", "label": "Template", "options": "HD Ticket Template"}, {"collapsible": 1, "fieldname": "service_level_section", "fieldtype": "Section Break"}, {"fieldname": "sla", "fieldtype": "Link", "label": "SLA", "options": "HD Service Level Agreement"}, {"depends_on": "eval: doc.status_category != 'Paused' && doc.sla;", "fieldname": "response_by", "fieldtype": "Datetime", "label": "Response By", "read_only": 1}, {"collapsible": 1, "fieldname": "cb", "fieldtype": "Column Break", "options": "fa fa-pushpin", "read_only": 1}, {"depends_on": "eval: doc.sla", "fieldname": "agreement_status", "fieldtype": "Select", "label": "SLA Status", "options": "\nFirst Response Due\nResolution Due\nFailed\nFulfilled\nPaused", "read_only": 1}, {"depends_on": "eval: doc.status_category != 'Paused' && doc.sla;", "fieldname": "resolution_by", "fieldtype": "Datetime", "label": "Resolution By", "read_only": 1}, {"fieldname": "service_level_agreement_creation", "fieldtype": "Datetime", "hidden": 1, "label": "SLA Creation", "read_only": 1}, {"fieldname": "on_hold_since", "fieldtype": "Datetime", "label": "On Hold Since", "read_only": 1}, {"fieldname": "total_hold_time", "fieldtype": "Duration", "label": "Total Hold Time", "read_only": 1}, {"collapsible": 1, "fieldname": "response", "fieldtype": "Section Break"}, {"bold": 1, "fieldname": "first_response_time", "fieldtype": "Duration", "label": "First Response Time", "read_only": 1}, {"fieldname": "first_responded_on", "fieldtype": "Datetime", "label": "First Responded On"}, {"fieldname": "column_break_26", "fieldtype": "Column Break"}, {"bold": 1, "fieldname": "avg_response_time", "fieldtype": "Duration", "label": "Average Response Time", "read_only": 1}, {"collapsible": 1, "fieldname": "section_break_19", "fieldtype": "Section Break"}, {"depends_on": "eval:!doc.__islocal", "fieldname": "resolution_details", "fieldtype": "Text Editor", "label": "Resolution Details", "no_copy": 1, "oldfieldname": "resolution_details", "oldfieldtype": "Text"}, {"depends_on": "eval:!doc.__islocal", "fieldname": "column_break1", "fieldtype": "Column Break", "oldfieldtype": "Column Break", "read_only": 1}, {"default": "Today", "fieldname": "opening_date", "fieldtype": "Date", "label": "Opening Date", "no_copy": 1, "oldfieldname": "opening_date", "oldfieldtype": "Date", "read_only": 1}, {"fieldname": "opening_time", "fieldtype": "Time", "label": "Opening Time", "no_copy": 1, "oldfieldname": "opening_time", "oldfieldtype": "Time", "read_only": 1}, {"depends_on": "eval:!doc.__islocal", "fieldname": "resolution_date", "fieldtype": "Datetime", "label": "Resolution Date", "no_copy": 1, "oldfieldname": "resolution_date", "oldfieldtype": "Date", "read_only": 1}, {"fieldname": "resolution_time", "fieldtype": "Duration", "label": "Resolution Time", "read_only": 1}, {"fieldname": "user_resolution_time", "fieldtype": "Duration", "label": "User Resolution Time", "read_only": 1}, {"collapsible": 1, "fieldname": "additional_info", "fieldtype": "Section Break", "options": "fa fa-pushpin", "read_only": 1}, {"fieldname": "contact", "fieldtype": "Link", "label": "Contact", "options": "Contact"}, {"fieldname": "customer", "fieldtype": "Link", "in_standard_filter": 1, "label": "Customer", "options": "HD Customer"}, {"fieldname": "email_account", "fieldtype": "Link", "label": "<PERSON><PERSON> Account", "options": "<PERSON><PERSON> Account"}, {"fieldname": "column_break_16", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "via_customer_portal", "fieldtype": "Check", "label": "Via Customer Portal"}, {"fieldname": "attachment", "fieldtype": "Attach", "hidden": 1, "label": "Attachment"}, {"fieldname": "content_type", "fieldtype": "Data", "hidden": 1, "label": "Content Type"}, {"fieldname": "customer_feedback_section", "fieldtype": "Section Break"}, {"fieldname": "feedback_extra", "fieldtype": "Long Text", "label": "<PERSON><PERSON><PERSON> (Extra)"}, {"fieldname": "feedback", "fieldtype": "Link", "label": "Feedback (Option)", "options": "HD Ticket Feedback Option"}, {"fieldname": "feedback_rating", "fieldtype": "Rating", "label": "Rating"}, {"fieldname": "sla_tab", "fieldtype": "Tab Break", "label": "SLA"}, {"fieldname": "response_tab", "fieldtype": "Tab Break", "label": "Response"}, {"fieldname": "resolution_tab", "fieldtype": "Tab Break", "label": "Resolution"}, {"fieldname": "reference_tab", "fieldtype": "Tab Break", "label": "Reference"}, {"fieldname": "feedback_tab", "fieldtype": "Tab Break", "label": "<PERSON><PERSON><PERSON>"}, {"fieldname": "meta_tab", "fieldtype": "Tab Break", "label": "Meta"}, {"fieldname": "summary", "fieldtype": "Text Editor", "label": "Summary"}, {"fieldname": "split_and_merge_section", "fieldtype": "Section Break", "label": "Split and Merge"}, {"default": "0", "fieldname": "is_merged", "fieldtype": "Check", "label": "Ticket <PERSON>d"}, {"depends_on": "eval:doc.is_merged", "fieldname": "merged_with", "fieldtype": "Link", "label": "Merged With", "options": "HD Ticket", "read_only": 1}, {"fieldname": "key", "fieldtype": "Data", "label": "Key", "read_only": 1, "set_only_once": 1}, {"fetch_from": "status.category", "fieldname": "status_category", "fieldtype": "Data", "label": "Status Category", "read_only": 1}], "grid_page_length": 50, "icon": "fa fa-issue", "idx": 61, "links": [], "modified": "2025-09-04 19:44:36.006061", "modified_by": "Administrator", "module": "Helpdesk", "name": "HD Ticket", "naming_rule": "Autoincrement", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Agent", "share": 1, "write": 1}, {"create": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "All", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Agent Manager", "share": 1, "write": 1}], "quick_entry": 1, "recipient_account_field": "email_account", "row_format": "Dynamic", "search_fields": "status,subject,raised_by", "sender_field": "raised_by", "sort_field": "modified", "sort_order": "DESC", "states": [], "subject_field": "subject", "timeline_field": "contact", "title_field": "subject", "track_changes": 1, "track_seen": 1}