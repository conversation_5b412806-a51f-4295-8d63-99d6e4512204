{"actions": [], "creation": "2017-02-17 13:07:35.686409", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["priority_section", "default_priority", "column_break_nvbf", "agent_groups_section", "restrict_tickets_by_agent_group", "do_not_restrict_tickets_without_an_agent_group", "assign_within_team", "column_break_dxlq", "assignment_rules_section", "base_support_rotation", "knowledge_base_section", "prefer_knowledge_base", "ticket_tab", "ticket_type_section", "default_ticket_type", "is_ticket_type_mandatory", "column_break_zxek", "feedback_section", "is_feedback_mandatory", "column_break_kegb", "ticket_restrictions_section", "allow_anyone_to_create_tickets", "auto_update_status", "update_status_to", "column_break_iarl", "auto_close_tickets", "auto_close_status", "auto_close_after_days", "status_section", "default_ticket_status", "column_break_yfbu", "ticket_reopen_status", "workflow_tab", "skip_email_workflow", "instantly_send_email", "column_break_aomm", "branding_tab", "images_column", "brand_logo", "column_break_fsjn", "misc_tab", "setup_section", "setup_complete", "initial_helpdesk_name_setup_skipped", "column_break_hjfh", "search_tab", "name_weight", "subject_weight", "column_break_qoxt", "description_weight", "headings_weight", "email_customisations_tab", "share_feedback_section", "enable_email_ticket_feedback", "send_email_feedback_on_status", "feedback_email_content", "acknowledgement_section", "send_acknowledgement_email", "acknowledgement_email_content", "reply_from_contact_section", "enable_reply_email_to_agent", "reply_email_to_agent_content", "reply_from_agent_section", "enable_reply_email_via_agent", "reply_via_agent_email_content"], "fields": [{"fieldname": "assignment_rules_section", "fieldtype": "Section Break", "hidden": 1, "label": "Assignment Rules"}, {"fieldname": "base_support_rotation", "fieldtype": "Link", "hidden": 1, "label": "Base Support Rotation", "options": "Assignment Rule", "read_only": 1}, {"fieldname": "knowledge_base_section", "fieldtype": "Section Break", "label": "Knowledge Base"}, {"fieldname": "agent_groups_section", "fieldtype": "Section Break", "label": "Team Restrictions"}, {"default": "0", "fieldname": "restrict_tickets_by_agent_group", "fieldtype": "Check", "label": "Restrict tickets by Team"}, {"fieldname": "column_break_dxlq", "fieldtype": "Column Break"}, {"default": "0", "depends_on": "restrict_tickets_by_agent_group", "fieldname": "do_not_restrict_tickets_without_an_agent_group", "fieldtype": "Check", "label": "Do not restrict tickets without a Team"}, {"fieldname": "misc_tab", "fieldtype": "Tab Break", "label": "Misc"}, {"fieldname": "setup_section", "fieldtype": "Section Break", "label": "Setup"}, {"default": "0", "fieldname": "setup_complete", "fieldtype": "Check", "label": "Is setup complete"}, {"default": "0", "fieldname": "initial_helpdesk_name_setup_skipped", "fieldtype": "Check", "label": "Is name setup skipped"}, {"fieldname": "column_break_hjfh", "fieldtype": "Column Break"}, {"fieldname": "ticket_type_section", "fieldtype": "Section Break", "label": "Ticket Type"}, {"fieldname": "default_ticket_type", "fieldtype": "Link", "label": "Default ticket type", "options": "HD Ticket Type"}, {"default": "0", "fieldname": "is_ticket_type_mandatory", "fieldtype": "Check", "label": "Ticket type is mandatory"}, {"fieldname": "column_break_zxek", "fieldtype": "Column Break"}, {"fieldname": "workflow_tab", "fieldtype": "Tab Break", "label": "Workflow"}, {"default": "0", "description": "This field is used to skip email-related workflows for tickets. If this field is checked, no emails will be sent related to tickets, such as new ticket creation, status updates, or notifications.", "fieldname": "skip_email_workflow", "fieldtype": "Check", "label": "Skip e-mail workflow"}, {"fieldname": "column_break_aomm", "fieldtype": "Column Break"}, {"default": "0", "description": "This field is used to send an email instantly without adding it into the queue. If this field is checked, the email will be sent immediately after clicking the \"Send\" button, instead of being added to the email queue for later processing.", "fieldname": "instantly_send_email", "fieldtype": "Check", "label": "Instantly send e-mail"}, {"fieldname": "branding_tab", "fieldtype": "Tab Break", "label": "Branding"}, {"fieldname": "images_column", "fieldtype": "Column Break", "label": "Images"}, {"description": "Image to be used in various places, including Login and Signup pages. An image with transparent background and 160 x 32 is preferred", "fieldname": "brand_logo", "fieldtype": "Attach Image", "label": "Logo"}, {"fieldname": "column_break_fsjn", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "prefer_knowledge_base", "fieldtype": "Check", "label": "Prefer knowledge base"}, {"fieldname": "priority_section", "fieldtype": "Section Break", "label": "Priority"}, {"fieldname": "default_priority", "fieldtype": "Link", "label": "Default priority", "options": "HD Ticket Priority"}, {"fieldname": "column_break_nvbf", "fieldtype": "Column Break"}, {"fieldname": "search_tab", "fieldtype": "Tab Break", "label": "Search"}, {"default": "1", "fieldname": "name_weight", "fieldtype": "Int", "label": "Name Weight"}, {"default": "6", "fieldname": "subject_weight", "fieldtype": "Int", "label": "Subject Weight"}, {"default": "5", "fieldname": "description_weight", "fieldtype": "Int", "label": "Description Weight"}, {"fieldname": "column_break_qoxt", "fieldtype": "Column Break"}, {"default": "8", "fieldname": "headings_weight", "fieldtype": "Int", "label": "Headings Weight"}, {"fieldname": "ticket_tab", "fieldtype": "Tab Break", "label": "Ticket"}, {"fieldname": "ticket_restrictions_section", "fieldtype": "Section Break"}, {"default": "0", "description": "If enabled, anyone will be able to create tickets (without any permission). ", "fieldname": "allow_anyone_to_create_tickets", "fieldtype": "Check", "label": "Allow anyone to create tickets"}, {"default": "0", "description": "When enabled, the ticket status will automatically change to the status selected below whenever the agent responds to a ticket.\n", "fieldname": "auto_update_status", "fieldtype": "Check", "label": "Auto update status"}, {"default": "1", "description": "If enabled, the feedback dialog will be shown, when a user tries to close a ticket. \n", "fieldname": "is_feedback_mandatory", "fieldtype": "Check", "label": "Enable feedback for Customer Portal"}, {"fieldname": "column_break_iarl", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "auto_close_tickets", "fieldtype": "Check", "label": "Automatically Close Tickets when"}, {"default": "14", "depends_on": "auto_close_tickets", "fieldname": "auto_close_after_days", "fieldtype": "Int", "label": "Auto-close after (Days)", "mandatory_depends_on": "eval: doc.auto_close_tickets"}, {"fieldname": "feedback_section", "fieldtype": "Section Break", "label": "<PERSON><PERSON><PERSON>"}, {"fieldname": "column_break_kegb", "fieldtype": "Column Break"}, {"depends_on": "eval:doc.enable_email_ticket_feedback", "fieldname": "send_email_feedback_on_status", "fieldtype": "Link", "label": "Send feedback when", "link_filters": "[[\"HD Ticket Status\",\"category\",\"=\",\"Resolved\"]]", "mandatory_depends_on": "eval:doc.enable_email_ticket_feedback", "options": "HD Ticket Status"}, {"default": "0", "description": "If enabled, feedback email will be sent to the customer when you mark a ticket as the status selected below.\n", "fieldname": "enable_email_ticket_feedback", "fieldtype": "Check", "label": "Enabled"}, {"default": "0", "depends_on": "restrict_tickets_by_agent_group", "fieldname": "assign_within_team", "fieldtype": "Check", "label": "Restrict agent assignment to selected Team"}, {"fieldname": "email_customisations_tab", "fieldtype": "Tab Break", "label": "Email Notifications"}, {"fieldname": "share_feedback_section", "fieldtype": "Section Break", "label": "Share Feedback"}, {"fieldname": "feedback_email_content", "fieldtype": "Text", "label": "Email Content"}, {"fieldname": "acknowledgement_section", "fieldtype": "Section Break", "label": "Acknowledgement"}, {"fieldname": "acknowledgement_email_content", "fieldtype": "Text", "label": "Email Content"}, {"default": "0", "description": "If enabled, a ticket creation acknowledgement email will be sent to the user right after creating an email ticket", "fieldname": "send_acknowledgement_email", "fieldtype": "Check", "label": "Enabled"}, {"default": "1", "description": "If enabled, an email will be sent to all of the assigned agents after a reply from one of the contacts", "fieldname": "enable_reply_email_to_agent", "fieldtype": "Check", "label": "Enabled"}, {"fieldname": "reply_email_to_agent_content", "fieldtype": "Text", "label": "Email Content"}, {"default": "1", "description": "If enabled, an email is sent to all of the recipients associated with an agent's reply", "fieldname": "enable_reply_email_via_agent", "fieldtype": "Check", "label": "Enabled"}, {"fieldname": "reply_via_agent_email_content", "fieldtype": "Text", "label": "Email Content"}, {"fieldname": "reply_from_contact_section", "fieldtype": "Section Break", "label": "Reply From Contact"}, {"fieldname": "reply_from_agent_section", "fieldtype": "Section Break", "label": "Reply From Agent"}, {"depends_on": "eval:doc.auto_update_status", "fieldname": "update_status_to", "fieldtype": "Link", "label": "Update status to", "link_filters": "[]", "mandatory_depends_on": "eval:doc.auto_update_status", "options": "HD Ticket Status"}, {"fieldname": "status_section", "fieldtype": "Section Break", "label": "Status"}, {"description": "Status of the ticket, when it is created in the system.", "fieldname": "default_ticket_status", "fieldtype": "Link", "in_list_view": 1, "label": "Default ticket status", "link_filters": "[[\"HD Ticket Status\",\"category\",\"=\",\"Open\"]]", "options": "HD Ticket Status", "reqd": 1}, {"fieldname": "column_break_yfbu", "fieldtype": "Column Break"}, {"description": "Status of the ticket, when a customer replies on the ticket.", "fieldname": "ticket_reopen_status", "fieldtype": "Link", "in_list_view": 1, "label": "Ticket Reopen status", "link_filters": "[[\"HD Ticket Status\",\"category\",\"=\",\"Open\"]]", "options": "HD Ticket Status", "reqd": 1}, {"depends_on": "auto_close_tickets", "fieldname": "auto_close_status", "fieldtype": "Link", "label": "Status ", "link_filters": "[[\"HD Ticket Status\",\"category\",\"in\",[\"Paused\",\"Resolved\",null]]]", "mandatory_depends_on": "eval: doc.auto_close_tickets", "options": "HD Ticket Status"}], "grid_page_length": 50, "issingle": 1, "links": [], "modified": "2025-08-29 13:34:17.018321", "modified_by": "Administrator", "module": "Helpdesk", "name": "HD Settings", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "role": "System Manager", "share": 1, "write": 1}, {"email": 1, "print": 1, "read": 1, "role": "Agent", "share": 1}, {"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "role": "Agent Manager", "share": 1, "write": 1}], "quick_entry": 1, "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}